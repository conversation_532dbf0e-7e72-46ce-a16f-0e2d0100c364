{"deep_learning": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:26:37.419455"}, "advanced": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:26:37.419951"}, "reinforcement_learning": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:26:37.419951"}, "quantum": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:26:37.420452"}, "memory_learning": {"has_predict": false, "has_train": false, "is_valid": false, "validation_time": "2025-06-13T18:26:37.420452"}, "continuous_learning": {"has_predict": false, "has_train": false, "is_valid": false, "validation_time": "2025-06-13T18:26:37.420452"}, "adaptive_rl": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:26:37.420452"}, "dual_network": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:26:37.420953"}, "federated_learning": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:26:37.420953"}}