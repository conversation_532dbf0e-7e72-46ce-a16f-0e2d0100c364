{"deep_learning": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:08:43.477636"}, "advanced": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:08:43.478136"}, "reinforcement_learning": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:08:43.478136"}, "quantum": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:08:43.478637"}, "memory_learning": {"has_predict": false, "has_train": false, "is_valid": false, "validation_time": "2025-06-13T18:08:43.478637"}, "continuous_learning": {"has_predict": false, "has_train": false, "is_valid": false, "validation_time": "2025-06-13T18:08:43.479637"}, "adaptive_rl": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:08:43.480137"}, "dual_network": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:08:43.480137"}, "federated_learning": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:08:43.480137"}}