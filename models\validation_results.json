{"deep_learning": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:06:38.503007"}, "advanced": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:06:38.503509"}, "reinforcement_learning": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:06:38.503509"}, "quantum": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:06:38.504008"}, "memory_learning": {"has_predict": false, "has_train": false, "is_valid": false, "validation_time": "2025-06-13T18:06:38.504008"}, "continuous_learning": {"has_predict": false, "has_train": false, "is_valid": false, "validation_time": "2025-06-13T18:06:38.504008"}, "adaptive_rl": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:06:38.504008"}, "dual_network": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:06:38.504509"}, "federated_learning": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:06:38.504509"}}