{"deep_learning": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:44:38.761410"}, "advanced": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:44:38.761410"}, "reinforcement_learning": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:44:38.761911"}, "quantum": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:44:38.761911"}, "memory_learning": {"has_predict": false, "has_train": false, "is_valid": false, "validation_time": "2025-06-13T18:44:38.762411"}, "continuous_learning": {"has_predict": false, "has_train": false, "is_valid": false, "validation_time": "2025-06-13T18:44:38.762411"}, "adaptive_rl": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:44:38.762411"}, "dual_network": {"has_predict": true, "has_train": true, "is_valid": true, "validation_time": "2025-06-13T18:44:38.762411"}, "federated_learning": {"has_predict": true, "has_train": false, "is_valid": true, "validation_time": "2025-06-13T18:44:38.762911"}}