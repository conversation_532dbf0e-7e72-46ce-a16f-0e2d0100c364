import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
import json
import os
import sys
import logging
from typing import List, Optional, Dict, Any, Tuple, Union
import ta
import time
import requests
import threading
import multiprocessing
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
from dotenv import load_dotenv

# Import safe technical indicators
try:
    from technical_indicators_safe import add_safe_indicators, safe_adx, safe_dmi
    HAS_SAFE_INDICATORS = True
except ImportError:
    HAS_SAFE_INDICATORS = False

# Import ta library patch
try:
    from ta_library_patch import patch_ta_library, restore_ta_library, suppress_ta_warnings
    # Suppress all warnings from ta library globally
    suppress_ta_warnings()
    HAS_TA_PATCH = True
except ImportError:
    HAS_TA_PATCH = False
from market_microstructure import MarketMicrostructureAnalyzer
from options_analysis import OptionsAnalyzer
from sentiment_analyzer import SentimentAnalyzer
from reinforcement_learning import RLAgent
from execution import ExecutionEngine

# Import new advanced modules
try:
    from deep_learning_prediction import DeepLearningPredictor
    from high_frequency import HighFrequencyTrader
    from arbitrage import ArbitrageDetector
    from advanced_prediction import AdvancedPredictor
    from market_regime import MarketRegimeDetector
    from advanced_trading import AdvancedTradingFeatures
    from quantum_agent import QuantumAgent

    # Import cutting-edge modules
    from multi_timeframe import MultiTimeframeAnalyzer
    from alternative_data import AlternativeDataIntegrator
    from quantum_optimization import QuantumOptimizer
    from federated_learning import FederatedLearner
    from explainable_ai import ExplainableAI
    from adaptive_rl import AdaptiveRLAgent
    from adaptive_market import AdaptiveMarketAnalyzer
    from dual_network import DualNetworkModel
    from performance_tracker import PerformanceTracker

    # Import config module
    import config

    HAS_ADVANCED_MODULES = True
except ImportError as e:
    logging.warning(f"Advanced trading modules not available: {str(e)}")
    HAS_ADVANCED_MODULES = False

# Load environment variables from .env file
load_dotenv()

# Get API keys from environment variables
ALPHA_VANTAGE_API_KEY = os.getenv('ALPHA_VANTAGE_API_KEY')
POLYGON_API_KEY = os.getenv('POLYGON_API_KEY')

class HybridTradingBot:
    def _get_env_var(self, name: str, default: Any) -> Any:
        """Get environment variable with type conversion"""
        try:
            value = os.environ.get(name)
            if value is None:
                return default

            # Convert to appropriate type based on default
            if isinstance(default, int):
                return int(value)
            elif isinstance(default, float):
                return float(value)
            elif isinstance(default, bool):
                return value.lower() in ('true', 'yes', '1', 't', 'y')
            else:
                return value
        except Exception:
            return default

    def __init__(self, initial_balance: float = 100000, max_positions: int = 5,
                 stop_loss: float = 0.005, take_profit: float = 0.01,
                 trailing_stop: float = 0.008, initialize_models: bool = True):
        # Setup logging
        self.logger = logging.getLogger(__name__)

        # Core trading parameters
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.max_positions = max_positions
        self.stop_loss = stop_loss
        self.take_profit = take_profit
        self.trailing_stop = trailing_stop
        self.trading_mode = "paper"  # Default to paper trading mode
        self.watched_symbols = ["BTC/USD", "ETH/USD", "AAPL", "MSFT", "GOOGL"]  # Default watchlist
        self.status = "idle"  # Bot status: idle, running, paused, etc.

        # Fast trading parameters for aggressive profit generation
        self.fast_trading_mode = True  # Enable aggressive fast trading
        self.min_profit_threshold = 0.003  # 0.3% minimum profit to sell
        self.max_hold_time_minutes = 30  # Maximum time to hold a position (30 minutes)
        self.scalping_mode = True  # Enable scalping for quick profits
        self.profit_target_multiplier = 2.0  # Aim for 2x the minimum profit when possible

        # Flag to track if models have been initialized
        self.models_initialized = False

        # Advanced trading parameters
        self.scan_interval = 30  # Reduced to 30 seconds for faster response
        self.position_size_factor = 0.4  # 40% of balance per position
        self.min_volume = 1000000  # Minimum daily volume requirement
        self.min_price = 10.0  # Minimum stock price
        self.max_volatility = 0.05  # Maximum daily volatility
        self.base_risk_per_trade = 0.02  # Base risk per trade (2% of account)
        self.risk_per_trade = 0.02  # Current risk per trade (adjusted by market regime)

        # Trading state
        self.positions = {}
        self.watched_symbols = []
        self.last_prices = {}
        self.highest_prices = {}
        self.trades_history = []  # For execution quality analysis

        # Hardware optimization settings
        self.use_parallel = self._get_env_var('TRADING_BOT_PARALLEL_JOBS', 1) > 1
        self.batch_size = self._get_env_var('TRADING_BOT_BATCH_SIZE', 32)
        self.model_complexity = os.environ.get('TRADING_BOT_MODEL_COMPLEXITY', 'medium')
        self.max_symbols_limit = self._get_env_var('TRADING_BOT_MAX_SYMBOLS', 50)
        self.parallel_jobs = self._get_env_var('TRADING_BOT_PARALLEL_JOBS', 1)

        # Adjust settings based on hardware
        if self.parallel_jobs > 1:
            self.logger.info(f"Using parallel processing with {self.parallel_jobs} workers")
        if self.model_complexity == 'high':
            self.logger.info("Using high complexity models for better predictions")
        self.logger.info(f"Batch size: {self.batch_size}, Max symbols: {self.max_symbols_limit}")

        # Performance tracking
        self.total_trades = 0
        self.winning_trades = 0
        self.total_profit = 0.0
        self.daily_profit = 0.0
        self.max_drawdown = 0.0

        # Running state
        self.running = False

        # API request tracking
        self.yahoo_request_count = 0
        self.YAHOO_DAILY_LIMIT = 2000

        # Data cache
        self.data_cache = {}
        self.last_cache_time = {}
        self.cache_timeout = 3600  # 1 hour

        # Advanced components

        # Quantum components
        self.quantum_agent = None  # Will be set by register_quantum_agent
        self.use_quantum_agent = False  # Flag to enable quantum agent
        self.quantum_signals = {}  # Quantum signals by symbol
        try:
            self.microstructure_analyzer = MarketMicrostructureAnalyzer({})
        except Exception as e:
            self.logger.warning(f"Failed to initialize MarketMicrostructureAnalyzer: {str(e)}")
            self.microstructure_analyzer = None

        try:
            self.options_analyzer = OptionsAnalyzer({})
        except Exception as e:
            self.logger.warning(f"Failed to initialize OptionsAnalyzer: {str(e)}")
            self.options_analyzer = None

        try:
            self.sentiment_analyzer = SentimentAnalyzer({})
        except Exception as e:
            self.logger.warning(f"Failed to initialize SentimentAnalyzer: {str(e)}")
            self.sentiment_analyzer = None

        try:
            self.rl_trader = RLAgent({})
        except Exception as e:
            self.logger.warning(f"Failed to initialize RLAgent: {str(e)}")
            self.rl_trader = None

        try:
            self.execution_engine = ExecutionEngine({})
        except Exception as e:
            self.logger.warning(f"Failed to initialize ExecutionEngine: {str(e)}")
            self.execution_engine = None

        # Create references to all advanced modules but don't initialize them yet
        self.deep_learning_predictor = None
        self.data_update_agent = None
        self.high_frequency_trader = None
        self.arbitrage_detector = None
        self.advanced_predictor = None
        self.memory_learning = None
        self.continuous_learning_engine = None
        self.market_regime_detector = None
        self.advanced_trading = None
        self.multi_timeframe = None
        self.alternative_data = None
        self.quantum_optimizer = None
        self.quantum_agent = None
        self.use_quantum_agent = False
        self.federated_learner = None
        self.explainable_ai = None
        self.adaptive_rl = None
        self.adaptive_market = None
        self.dual_network = None
        self.performance_tracker = None

        # Initialize advanced modules if available and if initialize_models is True
        if HAS_ADVANCED_MODULES and initialize_models:
            self._initialize_advanced_modules()

        # Initialize model management systems
        self.model_manager = None
        self.model_integration_system = None
        self._initialize_model_systems()

        # Data caching
        self.data_cache = {}
        self.cache_timeout = 300
        self.last_cache_time = {}

        # Setup logging (if not already set up)
        if not self.logger.handlers:
            self.setup_logging()

    def _initialize_advanced_modules(self):
        """Initialize all advanced modules"""
        self.logger.info("Initializing advanced modules...")

        if not HAS_ADVANCED_MODULES:
            self.logger.warning("Advanced modules are not available")
            return

        # Initialize Deep Learning Predictor
        try:
            from deep_learning_prediction import DeepLearningPredictor
            self.deep_learning_predictor = DeepLearningPredictor()
            self.logger.info("Deep Learning Predictor initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize DeepLearningPredictor: {str(e)}")
            self.deep_learning_predictor = None

        # Initialize Data Update Agent
        try:
            from data_update_agent import DataUpdateAgent
            self.data_update_agent = DataUpdateAgent()
            self.logger.info("Data Update Agent initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize DataUpdateAgent: {str(e)}")
            self.data_update_agent = None

        # Initialize High Frequency Trader
        try:
            from high_frequency import HighFrequencyTrader
            self.high_frequency_trader = HighFrequencyTrader()
            self.logger.info("High Frequency Trader initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize HighFrequencyTrader: {str(e)}")
            self.high_frequency_trader = None

        # Initialize Arbitrage Detector
        try:
            from arbitrage import ArbitrageDetector
            self.arbitrage_detector = ArbitrageDetector()
            self.logger.info("Arbitrage Detector initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize ArbitrageDetector: {str(e)}")
            self.arbitrage_detector = None

        # Initialize Advanced Predictor
        try:
            from advanced_prediction import AdvancedPredictor
            self.advanced_predictor = AdvancedPredictor()
            self.logger.info("Advanced Predictor initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize AdvancedPredictor: {str(e)}")
            self.advanced_predictor = None

        # Initialize Memory-Augmented Learning
        try:
            from memory_augmented_learning import MemoryAugmentedLearning
            memory_config = {
                'max_buffer_size': 100000,
                'data_dir': 'data/memory_learning',
                'use_memory_trader': True,
                'trader_config': {
                    'memory_size': 8192,  # Large memory for better pattern recognition
                    'memory_width': 256,  # Wide vector for detailed market state encoding
                    'read_heads': 4       # Multiple read heads for parallel memory access
                }
            }
            self.memory_learning = MemoryAugmentedLearning(memory_config)
            self.logger.info("Memory-Augmented Learning initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize MemoryAugmentedLearning: {str(e)}")
            self.memory_learning = None

        # Initialize Continuous Learning Engine
        try:
            from continuous_learning_engine import ContinuousLearningEngine
            continuous_learning_config = {
                'memory_learning_config': memory_config if 'memory_config' in locals() else {},
                'learning_rate': 0.01,
                'error_threshold': 0.02,
                'confidence_threshold': 0.7,
                'min_samples_for_learning': 50
            }
            self.continuous_learning_engine = ContinuousLearningEngine(continuous_learning_config)
            self.logger.info("Continuous Learning Engine initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize ContinuousLearningEngine: {str(e)}")
            self.continuous_learning_engine = None

        # Initialize Market Regime Detector
        try:
            from market_regime import MarketRegimeDetector
            self.market_regime_detector = MarketRegimeDetector()
            self.logger.info("Market Regime Detector initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize MarketRegimeDetector: {str(e)}")
            self.market_regime_detector = None

        # Initialize Advanced Trading Features
        try:
            from advanced_trading import AdvancedTradingFeatures
            self.advanced_trading = AdvancedTradingFeatures()
            self.logger.info("Advanced Trading Features initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize AdvancedTradingFeatures: {str(e)}")
            self.advanced_trading = None

        # Initialize Multi-Timeframe Analyzer
        try:
            from multi_timeframe import MultiTimeframeAnalyzer
            self.multi_timeframe = MultiTimeframeAnalyzer()
            self.logger.info("Multi-Timeframe Analyzer initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize MultiTimeframeAnalyzer: {str(e)}")
            self.multi_timeframe = None

        # Initialize Alternative Data Integrator
        try:
            from alternative_data import AlternativeDataIntegrator
            self.alternative_data = AlternativeDataIntegrator()
            self.logger.info("Alternative Data Integrator initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize AlternativeDataIntegrator: {str(e)}")
            self.alternative_data = None

        # Initialize Quantum Optimizer
        try:
            from quantum_optimization import QuantumOptimizer
            self.quantum_optimizer = QuantumOptimizer()
            self.logger.info("Quantum Optimizer initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize QuantumOptimizer: {str(e)}")
            self.quantum_optimizer = None

        # Initialize Quantum Agent
        try:
            from quantum_agent import QuantumAgent
            self.quantum_agent = QuantumAgent(agent_id="trading_bot_quantum_agent")
            self.use_quantum_agent = True
            self.logger.info("Quantum Agent initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize QuantumAgent: {str(e)}")
            self.quantum_agent = None
            self.use_quantum_agent = False

        # Initialize Federated Learner
        try:
            from federated_learning import FederatedLearner
            self.federated_learner = FederatedLearner()
            self.logger.info("Federated Learner initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize FederatedLearner: {str(e)}")
            self.federated_learner = None

        # Initialize Explainable AI
        try:
            from explainable_ai import ExplainableAI
            self.explainable_ai = ExplainableAI()
            self.logger.info("Explainable AI initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize ExplainableAI: {str(e)}")
            self.explainable_ai = None

        # Initialize Adaptive RL Agent
        try:
            from adaptive_rl import AdaptiveRLAgent
            self.adaptive_rl = AdaptiveRLAgent()
            self.logger.info("Adaptive RL Agent initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize AdaptiveRLAgent: {str(e)}")
            self.adaptive_rl = None

        # Initialize Adaptive Market Analyzer
        try:
            from adaptive_market import AdaptiveMarketAnalyzer
            self.adaptive_market = AdaptiveMarketAnalyzer()
            self.logger.info("Adaptive Market Analyzer initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize AdaptiveMarketAnalyzer: {str(e)}")
            self.adaptive_market = None

        # Initialize Dual Network Model
        try:
            from dual_network import DualNetworkModel
            self.dual_network = DualNetworkModel()
            self.logger.info("Dual Network Model initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize DualNetworkModel: {str(e)}")
            self.dual_network = None

        # Initialize Performance Tracker
        try:
            from performance_tracker import PerformanceTracker
            self.performance_tracker = PerformanceTracker()
            self.logger.info("Performance Tracker initialized successfully")
        except Exception as e:
            self.logger.warning(f"Failed to initialize PerformanceTracker: {str(e)}")
            self.performance_tracker = None

        # Set models_initialized flag to True
        self.models_initialized = True
        self.logger.info("All advanced modules initialized successfully")

    def _initialize_model_systems(self):
        """Initialize model management and integration systems"""
        try:
            self.logger.info("Initializing model management systems...")

            # Initialize Model Manager
            try:
                from model_manager import ModelManager
                model_config = {
                    'models_dir': 'models',
                    'auto_discovery': True,
                    'auto_update': True
                }
                self.model_manager = ModelManager(model_config)
                self.logger.info("Model Manager initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Model Manager: {str(e)}")
                self.model_manager = None

            # Initialize Model Integration System
            try:
                from model_integration_system import ModelIntegrationSystem
                integration_config = {
                    'auto_update': True,
                    'update_interval': 3600,  # 1 hour
                    'ensemble_predictions': True,
                    'model_validation': True
                }

                if self.model_manager:
                    self.model_integration_system = ModelIntegrationSystem(
                        self, self.model_manager, integration_config
                    )
                    self.logger.info("Model Integration System initialized successfully")
                else:
                    self.logger.warning("Cannot initialize Model Integration System without Model Manager")
                    self.model_integration_system = None

            except Exception as e:
                self.logger.warning(f"Failed to initialize Model Integration System: {str(e)}")
                self.model_integration_system = None

            self.logger.info("Model management systems initialization completed")

        except Exception as e:
            self.logger.error(f"Error initializing model systems: {str(e)}")
            self.model_manager = None
            self.model_integration_system = None

    def initialize_models(self):
        """Initialize all models if they haven't been initialized yet"""
        if self.models_initialized:
            self.logger.info("Models already initialized, skipping initialization")
            return True

        self.logger.info("Initializing models...")
        self._initialize_advanced_modules()
        return True

    def disable_model_training(self):
        """Disable model training to prevent CPU/memory usage during UI initialization"""
        self.logger.info("Disabling model training")

        # Disable training in continuous learning engine
        if hasattr(self, 'continuous_learning_engine') and self.continuous_learning_engine is not None:
            if hasattr(self.continuous_learning_engine, 'disable'):
                self.continuous_learning_engine.disable()
                self.logger.info("Disabled continuous learning engine")

        # Disable training in deep learning predictor
        if hasattr(self, 'deep_learning_predictor') and self.deep_learning_predictor is not None:
            if hasattr(self.deep_learning_predictor, 'disable_training'):
                self.deep_learning_predictor.disable_training()
                self.logger.info("Disabled deep learning predictor training")

        # Disable training in quantum agent
        if hasattr(self, 'quantum_agent') and self.quantum_agent is not None:
            if hasattr(self.quantum_agent, 'disable_training'):
                self.quantum_agent.disable_training()
                self.logger.info("Disabled quantum agent training")

        # Disable training in reinforcement learning agent
        if hasattr(self, 'rl_trader') and self.rl_trader is not None:
            if hasattr(self.rl_trader, 'disable_training'):
                self.rl_trader.disable_training()
                self.logger.info("Disabled reinforcement learning agent training")

        return True

    def enable_continuous_learning(self):
        """Enable continuous learning for all agents"""
        self.logger.info("Enabling continuous learning for all agents")

        # Enable continuous learning engine
        if hasattr(self, 'continuous_learning_engine') and self.continuous_learning_engine is not None:
            if hasattr(self.continuous_learning_engine, 'enable'):
                self.continuous_learning_engine.enable()
                self.logger.info("Enabled continuous learning engine")

        # Enable training in deep learning predictor
        if hasattr(self, 'deep_learning_predictor') and self.deep_learning_predictor is not None:
            if hasattr(self.deep_learning_predictor, 'enable_training'):
                self.deep_learning_predictor.enable_training()
                self.logger.info("Enabled deep learning predictor training")

        # Enable training in quantum agent
        if hasattr(self, 'quantum_agent') and self.quantum_agent is not None:
            if hasattr(self.quantum_agent, 'enable_training'):
                self.quantum_agent.enable_training()
                self.logger.info("Enabled quantum agent training")

        # Enable training in reinforcement learning agent
        if hasattr(self, 'rl_trader') and self.rl_trader is not None:
            if hasattr(self.rl_trader, 'enable_training'):
                self.rl_trader.enable_training()
                self.logger.info("Enabled reinforcement learning agent training")

        return True

    def disable_continuous_learning(self):
        """Disable continuous learning for all agents"""
        self.logger.info("Disabling continuous learning for all agents")

        # Disable continuous learning engine
        if hasattr(self, 'continuous_learning_engine') and self.continuous_learning_engine is not None:
            if hasattr(self.continuous_learning_engine, 'disable'):
                self.continuous_learning_engine.disable()
                self.logger.info("Disabled continuous learning engine")

        # Disable training in deep learning predictor
        if hasattr(self, 'deep_learning_predictor') and self.deep_learning_predictor is not None:
            if hasattr(self.deep_learning_predictor, 'disable_training'):
                self.deep_learning_predictor.disable_training()
                self.logger.info("Disabled deep learning predictor training")

        # Disable training in quantum agent
        if hasattr(self, 'quantum_agent') and self.quantum_agent is not None:
            if hasattr(self.quantum_agent, 'disable_training'):
                self.quantum_agent.disable_training()
                self.logger.info("Disabled quantum agent training")

        # Disable training in reinforcement learning agent
        if hasattr(self, 'rl_trader') and self.rl_trader is not None:
            if hasattr(self.rl_trader, 'disable_training'):
                self.rl_trader.disable_training()
                self.logger.info("Disabled reinforcement learning agent training")

        return True

    def start(self):
        """Start the trading bot and all its components"""
        self.logger.info("Starting trading bot")

        # Make sure models are initialized
        if not self.models_initialized:
            self.initialize_models()

        # Enable continuous learning
        self.enable_continuous_learning()

        # Set status to running
        self.status = "running"

        # Start data update agent if available
        if hasattr(self, 'data_update_agent') and self.data_update_agent is not None:
            if hasattr(self.data_update_agent, 'start'):
                self.data_update_agent.start()
                self.logger.info("Started data update agent")

        # Start high frequency trader if available
        if hasattr(self, 'high_frequency_trader') and self.high_frequency_trader is not None:
            if hasattr(self.high_frequency_trader, 'start'):
                self.high_frequency_trader.start()
                self.logger.info("Started high frequency trader")

        # Start performance tracker if available
        if hasattr(self, 'performance_tracker') and self.performance_tracker is not None:
            if hasattr(self.performance_tracker, 'start'):
                self.performance_tracker.start()
                self.logger.info("Started performance tracker")

        self.logger.info("Trading bot started successfully")
        return True

    def stop(self):
        """Stop the trading bot and all its components"""
        self.logger.info("Stopping trading bot")

        # Disable continuous learning
        self.disable_continuous_learning()

        # Set status to idle
        self.status = "idle"

        # Stop data update agent if available
        if hasattr(self, 'data_update_agent') and self.data_update_agent is not None:
            if hasattr(self.data_update_agent, 'stop'):
                self.data_update_agent.stop()
                self.logger.info("Stopped data update agent")

        # Stop high frequency trader if available
        if hasattr(self, 'high_frequency_trader') and self.high_frequency_trader is not None:
            if hasattr(self.high_frequency_trader, 'stop'):
                self.high_frequency_trader.stop()
                self.logger.info("Stopped high frequency trader")

        # Stop performance tracker if available
        if hasattr(self, 'performance_tracker') and self.performance_tracker is not None:
            if hasattr(self.performance_tracker, 'stop'):
                self.performance_tracker.stop()
                self.logger.info("Stopped performance tracker")

        self.logger.info("Trading bot stopped successfully")
        return True

    def emergency_stop(self):
        """Emergency stop - sell all positions and stop all components"""
        self.logger.warning("EMERGENCY STOP ACTIVATED - Selling all positions and stopping all components")

        # Sell all positions
        self.sell_all_positions()

        # Stop all components
        self.stop()

        # Set status to emergency stopped
        self.status = "emergency_stopped"

        self.logger.warning("Emergency stop completed - all positions sold and components stopped")
        return True

    def sell_all_positions(self):
        """Sell all current positions"""
        self.logger.info("Selling all positions")

        # Get list of current positions
        positions = list(self.positions.keys())

        # Sell each position
        for symbol in positions:
            try:
                self.sell(symbol)
                self.logger.info(f"Sold position in {symbol}")
            except Exception as e:
                self.logger.error(f"Error selling position in {symbol}: {str(e)}")

        self.logger.info(f"Sold all {len(positions)} positions")
        return True

    def run_simulation(self):
        """Run a trading simulation with aggressive fast trading"""
        try:
            self.logger.info("🚀 Starting AGGRESSIVE FAST TRADING simulation")

            # Switch to simulation mode
            self.switch_to_simulation_mode()

            # Enable fast trading mode
            self.fast_trading_mode = True
            self.scalping_mode = True

            # Run for a limited time or number of trades
            simulation_trades = 0
            max_simulation_trades = 50  # More trades for fast trading
            start_time = time.time()
            max_duration = 300  # 5 minutes max

            while (simulation_trades < max_simulation_trades and
                   self.running and
                   (time.time() - start_time) < max_duration):

                try:
                    # 1. Check existing positions for fast exits
                    self.check_positions()

                    # 2. Scan for opportunities
                    self.scan_for_opportunities()

                    # 3. Execute fast trading decisions
                    self._execute_fast_trading_cycle()

                    simulation_trades += 1

                    # Small delay between simulation steps (fast trading)
                    time.sleep(2)  # 2 seconds for aggressive trading

                except Exception as e:
                    self.logger.error(f"Error in trading cycle {simulation_trades}: {str(e)}")
                    time.sleep(5)

            self.logger.info(f"🎯 FAST TRADING simulation completed with {simulation_trades} cycles")
            return True

        except Exception as e:
            self.logger.error(f"Error running simulation: {str(e)}")
            return False

    def _execute_fast_trading_cycle(self):
        """Execute a fast trading cycle for rapid profit generation"""
        try:
            # Get available balance
            if self.balance < 10:  # Need at least $10 to trade
                return

            # Get top scoring stocks for fast trading
            symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "META", "NVDA", "TSLA"]

            for symbol in symbols:
                try:
                    # Skip if we already have a position
                    if symbol in self.positions:
                        continue

                    # Get market data
                    data = self.get_data(symbol)
                    if data is None or len(data) < 5:
                        continue

                    # Analyze for fast trading opportunity
                    score = self.analyze_stock(symbol, data)
                    current_price = data['Close'].iloc[-1]

                    # Fast trading decision logic
                    if score > 0.6 and self.balance >= current_price:
                        # Calculate position size for fast trading
                        position_size = min(1, int(self.balance * 0.1 / current_price))  # Use 10% of balance

                        if position_size > 0:
                            # Execute fast buy
                            success = self.buy_stock(symbol, position_size, current_price)
                            if success:
                                self.logger.info(f"🚀 FAST BUY: {symbol} x{position_size} @ ${current_price:.2f} (score: {score:.2f})")
                                break  # Only one trade per cycle for fast execution

                except Exception as e:
                    self.logger.error(f"Error in fast trading for {symbol}: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error in fast trading cycle: {str(e)}")

    def update_positions(self):
        """Update positions with current market data"""
        try:
            for symbol in list(self.positions.keys()):
                # Get current price
                data = self.get_data(symbol)
                if data is not None and not data.empty:
                    current_price = data['Close'].iloc[-1]
                    self.last_prices[symbol] = current_price

                    # Check for stop loss or take profit
                    position = self.positions[symbol]
                    entry_price = position['entry_price']

                    # Calculate profit/loss percentage
                    profit_pct = (current_price - entry_price) / entry_price

                    # Check stop loss
                    if hasattr(self, 'stop_loss') and profit_pct <= -self.stop_loss:
                        self.logger.info(f"Stop loss triggered for {symbol} at {profit_pct:.2%}")
                        self.sell_stock(symbol, current_price, "stop_loss")

                    # Check take profit
                    elif hasattr(self, 'take_profit') and profit_pct >= self.take_profit:
                        self.logger.info(f"Take profit triggered for {symbol} at {profit_pct:.2%}")
                        self.sell_stock(symbol, current_price, "take_profit")

        except Exception as e:
            self.logger.error(f"Error updating positions: {str(e)}")

    def get_watchlist(self):
        """Get the current watchlist"""
        return self.watched_symbols

    def add_to_watchlist(self, symbol):
        """Add a symbol to the watchlist"""
        if symbol not in self.watched_symbols:
            self.watched_symbols.append(symbol)
            self.logger.info(f"Added {symbol} to watchlist")
        return True

    def remove_from_watchlist(self, symbol):
        """Remove a symbol from the watchlist"""
        if symbol in self.watched_symbols:
            self.watched_symbols.remove(symbol)
            self.logger.info(f"Removed {symbol} from watchlist")
        return True

    def get_trading_signal(self, symbol):
        """Get trading signal for a symbol"""
        try:
            # Get data for the symbol
            data = self.get_data(symbol)

            if data is None or data.empty:
                self.logger.warning(f"No data available for {symbol}")
                return 0

            # Analyze market data
            analysis = self.analyze_market(symbol)

            if analysis is None or analysis.get('status') != 'success':
                self.logger.warning(f"Failed to analyze market data for {symbol}")
                return 0

            # Get overall signal
            signal = analysis.get('signals', {}).get('overall', 0)

            return signal
        except Exception as e:
            self.logger.error(f"Error getting trading signal for {symbol}: {str(e)}")
            return 0

    def get_performance_metrics(self):
        """Get performance metrics for the trading bot"""
        try:
            metrics = {
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'win_rate': self.winning_trades / self.total_trades if self.total_trades > 0 else 0,
                'total_profit': self.total_profit,
                'daily_profit': self.daily_profit,
                'max_drawdown': self.max_drawdown,
                'balance': self.balance,
                'positions': len(self.positions)
            }

            # Add performance tracker metrics if available
            if hasattr(self, 'performance_tracker') and self.performance_tracker is not None:
                if hasattr(self.performance_tracker, 'get_metrics'):
                    tracker_metrics = self.performance_tracker.get_metrics()
                    metrics.update(tracker_metrics)

            return metrics
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {str(e)}")
            return {}

    def switch_to_simulation_mode(self):
        """Switch to simulation mode"""
        self.logger.info("Switching to simulation mode")
        self.trading_mode = "simulation"
        self.is_simulation_mode = True

        # Reset balance to initial balance
        self.balance = self.initial_balance

        # Clear positions
        self.positions = {}

        # Log the change
        self.logger.info(f"Switched to simulation mode. Positions: {len(self.positions)}")

        return True

    def switch_to_live_mode(self):
        """Switch to live trading mode"""
        self.logger.info("Switching to live trading mode")
        self.trading_mode = "live"
        self.is_simulation_mode = False

        # Clear positions (will be populated from exchange)
        self.positions = {}

        # Log the change
        self.logger.info(f"Switched to live trading mode. Positions: {len(self.positions)}")

        return True

    def get_historical_data(self, symbol, period="1mo", interval="1d"):
        """Get historical data for a symbol"""
        try:
            return self.get_data(symbol, period=period, interval=interval)
        except Exception as e:
            self.logger.error(f"Error getting historical data for {symbol}: {str(e)}")
            return None

    def get_balance(self):
        """Get current balance"""
        return self.balance

    def setup_logging(self):
        """Setup logging configuration"""
        self.logger.setLevel(logging.INFO)

        # Create logs directory if it doesn't exist
        if not os.path.exists('logs'):
            os.makedirs('logs')

        # File handler
        file_handler = logging.FileHandler('logs/trading_bot.log')
        file_handler.setLevel(logging.INFO)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)

        # Create formatter
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def update_market_data(self, symbol, data):
        """
        Update market data for a specific symbol

        Args:
            symbol: The symbol to update
            data: DataFrame with market data
        """
        try:
            self.logger.info(f"Updating market data for {symbol}")

            # Add to watched symbols if not already there
            if symbol not in self.watched_symbols:
                self.watched_symbols.append(symbol)

            # Update last price
            if data is not None and not data.empty:
                self.last_prices[symbol] = data['Close'].iloc[-1]

            # Update data cache
            cache_key = f"{symbol}_1d"
            self.data_cache[cache_key] = data
            self.last_cache_time[cache_key] = datetime.now()

            # Check positions for this symbol
            if symbol in self.positions:
                self.check_position(symbol, data)

            return True
        except Exception as e:
            self.logger.error(f"Error updating market data for {symbol}: {str(e)}")
            return False

    def _get_predictions(self, symbol, data):
        """
        Get predictions from different models

        Args:
            symbol: The symbol to predict
            data: DataFrame with market data

        Returns:
            dict: Dictionary of predictions from different models
        """
        try:
            predictions = {}

            # Deep Learning prediction
            if hasattr(self, 'deep_learning_predictor') and self.deep_learning_predictor is not None:
                try:
                    dl_prediction = self.deep_learning_predictor.predict(symbol, data)
                    predictions['deep_learning'] = dl_prediction
                except Exception as e:
                    self.logger.warning(f"Error in deep learning prediction for {symbol}: {str(e)}")

            # Reinforcement Learning prediction
            if hasattr(self, 'rl_trader') and self.rl_trader is not None:
                try:
                    state = self._get_state_for_rl(data)
                    rl_prediction = self.rl_trader.get_action(state)
                    predictions['reinforcement_learning'] = rl_prediction
                except Exception as e:
                    self.logger.warning(f"Error in reinforcement learning prediction for {symbol}: {str(e)}")

            # Advanced Predictor
            if hasattr(self, 'advanced_predictor') and self.advanced_predictor is not None:
                try:
                    adv_prediction = self.advanced_predictor.predict(symbol, data)
                    predictions['advanced'] = adv_prediction
                except Exception as e:
                    self.logger.warning(f"Error in advanced prediction for {symbol}: {str(e)}")

            # Quantum Agent prediction
            if self.use_quantum_agent and self.quantum_agent is not None:
                try:
                    quantum_prediction = self.quantum_agent.predict(symbol, data)
                    predictions['quantum'] = quantum_prediction
                except Exception as e:
                    self.logger.warning(f"Error in quantum prediction for {symbol}: {str(e)}")

            # Ensemble prediction (average of all available predictions)
            if predictions:
                valid_predictions = [p for p in predictions.values() if isinstance(p, (int, float))]
                if valid_predictions:
                    predictions['ensemble'] = sum(valid_predictions) / len(valid_predictions)

            return predictions

        except Exception as e:
            self.logger.error(f"Error getting predictions for {symbol}: {str(e)}")
            return {}

    def _generate_signals(self, symbol, data, indicators, predictions):
        """
        Generate trading signals based on indicators and predictions

        Args:
            symbol: The symbol to generate signals for
            data: DataFrame with market data
            indicators: Dictionary of technical indicators
            predictions: Dictionary of model predictions

        Returns:
            dict: Dictionary of trading signals
        """
        try:
            signals = {}

            # Technical indicator signals

            # Moving Average Crossover
            if 'SMA_20' in indicators and 'SMA_50' in indicators:
                sma_20 = indicators['SMA_20'].iloc[-1]
                sma_50 = indicators['SMA_50'].iloc[-1]
                prev_sma_20 = indicators['SMA_20'].iloc[-2] if len(indicators['SMA_20']) > 1 else sma_20
                prev_sma_50 = indicators['SMA_50'].iloc[-2] if len(indicators['SMA_50']) > 1 else sma_50

                # Buy signal: SMA 20 crosses above SMA 50
                if prev_sma_20 <= prev_sma_50 and sma_20 > sma_50:
                    signals['ma_crossover'] = 1
                # Sell signal: SMA 20 crosses below SMA 50
                elif prev_sma_20 >= prev_sma_50 and sma_20 < sma_50:
                    signals['ma_crossover'] = -1
                else:
                    signals['ma_crossover'] = 0

            # MACD Signal
            if 'MACD' in indicators and 'Signal' in indicators:
                macd = indicators['MACD'].iloc[-1]
                signal = indicators['Signal'].iloc[-1]
                prev_macd = indicators['MACD'].iloc[-2] if len(indicators['MACD']) > 1 else macd
                prev_signal = indicators['Signal'].iloc[-2] if len(indicators['Signal']) > 1 else signal

                # Buy signal: MACD crosses above Signal line
                if prev_macd <= prev_signal and macd > signal:
                    signals['macd'] = 1
                # Sell signal: MACD crosses below Signal line
                elif prev_macd >= prev_signal and macd < signal:
                    signals['macd'] = -1
                else:
                    signals['macd'] = 0

            # RSI Signal
            if 'RSI' in indicators:
                rsi = indicators['RSI'].iloc[-1]

                # Buy signal: RSI below 30 (oversold)
                if rsi < 30:
                    signals['rsi'] = 1
                # Sell signal: RSI above 70 (overbought)
                elif rsi > 70:
                    signals['rsi'] = -1
                else:
                    signals['rsi'] = 0

            # Bollinger Bands Signal
            if 'BB_upper' in indicators and 'BB_lower' in indicators:
                price = data['Close'].iloc[-1]
                upper = indicators['BB_upper'].iloc[-1]
                lower = indicators['BB_lower'].iloc[-1]

                # Buy signal: Price below lower band
                if price < lower:
                    signals['bollinger'] = 1
                # Sell signal: Price above upper band
                elif price > upper:
                    signals['bollinger'] = -1
                else:
                    signals['bollinger'] = 0

            # Model prediction signals
            for model, prediction in predictions.items():
                if isinstance(prediction, (int, float)):
                    # Convert prediction to signal (-1, 0, 1)
                    if prediction > 0.55:  # Strong buy
                        signals[f'{model}_prediction'] = 1
                    elif prediction < 0.45:  # Strong sell
                        signals[f'{model}_prediction'] = -1
                    else:  # Hold
                        signals[f'{model}_prediction'] = 0

            # Calculate overall signal (average of all signals)
            signal_values = [s for s in signals.values() if isinstance(s, (int, float))]
            if signal_values:
                signals['overall'] = sum(signal_values) / len(signal_values)
            else:
                signals['overall'] = 0

            return signals

        except Exception as e:
            self.logger.error(f"Error generating signals for {symbol}: {str(e)}")
            return {'overall': 0}

    def analyze_market(self, symbol, timeframe='1d'):
        """
        Analyze market data for a specific symbol and timeframe

        Args:
            symbol: The symbol to analyze
            timeframe: The timeframe to analyze (default: 1d)

        Returns:
            dict: Analysis results including signals, indicators, and predictions
        """
        try:
            self.logger.info(f"Analyzing market data for {symbol} on {timeframe} timeframe")

            # Get data from cache
            cache_key = f"{symbol}_{timeframe}"
            data = self.data_cache.get(cache_key)

            if data is None or data.empty:
                self.logger.warning(f"No data available for {symbol} on {timeframe} timeframe")
                return {
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'status': 'error',
                    'message': 'No data available'
                }

            # Calculate technical indicators
            indicators = self._calculate_indicators(data)

            # Get predictions from different models
            predictions = self._get_predictions(symbol, data)

            # Get ensemble prediction from model integration system if available
            if self.model_integration_system:
                try:
                    ensemble_result = self.model_integration_system.get_ensemble_prediction(symbol, data)
                    if ensemble_result and ensemble_result.get('model_count', 0) > 0:
                        predictions['ensemble'] = ensemble_result['ensemble_prediction']
                        predictions['ensemble_confidence'] = ensemble_result['ensemble_confidence']
                        predictions['individual_predictions'] = ensemble_result['individual_predictions']
                        self.logger.debug(f"Got ensemble prediction for {symbol}: {ensemble_result['ensemble_prediction']:.4f} (confidence: {ensemble_result['ensemble_confidence']:.2f})")
                except Exception as e:
                    self.logger.warning(f"Error getting ensemble prediction for {symbol}: {str(e)}")

            # Generate trading signals
            signals = self._generate_signals(symbol, data, indicators, predictions)

            # Get market regime
            regime = self.market_regime_detector.detect_regime(data) if hasattr(self, 'market_regime_detector') else 'unknown'

            # Get sentiment if available
            sentiment = self.sentiment_analyzer.get_sentiment(symbol) if hasattr(self, 'sentiment_analyzer') else 0

            # Combine results
            analysis = {
                'symbol': symbol,
                'timeframe': timeframe,
                'timestamp': datetime.now().isoformat(),
                'last_price': self.last_prices.get(symbol, 0),
                'indicators': indicators,
                'predictions': predictions,
                'signals': signals,
                'regime': regime,
                'sentiment': sentiment,
                'status': 'success'
            }

            return analysis
        except Exception as e:
            self.logger.error(f"Error analyzing market for {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'status': 'error',
                'message': str(e)
            }

    def run_simulation(self, symbol=None, days=30, strategy="Reinforcement Learning"):
        """
        Run a trading simulation for a specific symbol or all watched symbols

        Args:
            symbol: The symbol to simulate (if None, simulates all watched symbols)
            days: Number of days to simulate
            strategy: Trading strategy to use

        Returns:
            dict or list: Simulation results including performance metrics
        """
        try:
            # Determine which symbols to simulate
            symbols_to_simulate = []
            if symbol is None or symbol.lower() == 'all':
                # Use watched_symbols if available
                if hasattr(self, 'watched_symbols') and self.watched_symbols:
                    symbols_to_simulate = self.watched_symbols
                # Use watchlist if available
                elif hasattr(self, 'watchlist') and self.watchlist:
                    symbols_to_simulate = self.watchlist
                # Fallback to default symbols
                else:
                    symbols_to_simulate = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META']
            else:
                symbols_to_simulate = [symbol]

            self.logger.info(f"Running simulation for {len(symbols_to_simulate)} symbols using {strategy} strategy for {days} days")

            # Initialize results list
            all_results = []

            # Run simulation for each symbol
            for symbol in symbols_to_simulate:
                try:
                    # Get historical data
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=days)

                    # Try to get data from cache first
                    cache_key = f"{symbol}_{days}d"
                    if cache_key in self.data_cache and (datetime.now() - self.last_cache_time.get(cache_key, datetime.min)).total_seconds() < self.cache_timeout:
                        data = self.data_cache[cache_key]
                    else:
                        # Get data from API
                        # Calculate period based on days
                        period = f"{days}d"
                        data = self.get_data(symbol, period=period)

                        # Cache data
                        if data is not None and not data.empty:
                            self.data_cache[cache_key] = data
                            self.last_cache_time[cache_key] = datetime.now()

                    if data is None or data.empty:
                        self.logger.warning(f"No data available for {symbol}")
                        all_results.append({
                            'symbol': symbol,
                            'strategy': strategy,
                            'status': 'error',
                            'message': 'No data available'
                        })
                        continue

                    # Initialize simulation variables
                    balance = self.initial_balance
                    position = 0
                    entry_price = 0
                    trades = []
                    portfolio_values = [balance]  # Track portfolio value over time

                    # Calculate indicators
                    indicators = self._calculate_indicators(data)

                    # Combine data with indicators
                    for col in indicators:
                        data[col] = indicators[col]

                    # Run simulation based on strategy
                    for i in range(1, len(data)):
                        current_price = data['Close'].iloc[i]

                        # Generate signal based on strategy
                        signal = 0

                        if strategy == "Reinforcement Learning" and hasattr(self, 'rl_trader'):
                            # Use RL agent for signal generation
                            state = self._get_state_for_rl(data.iloc[:i])
                            signal = self.rl_trader.get_action(state) if self.rl_trader else 0

                        elif strategy == "Deep Learning" and hasattr(self, 'deep_learning_predictor') and self.deep_learning_predictor is not None:
                            # Use deep learning for signal generation
                            try:
                                prediction = self.deep_learning_predictor.predict(symbol, data.iloc[:i])
                                if isinstance(prediction, dict):
                                    prediction_value = prediction.get('predicted_return', 0.5)
                                elif isinstance(prediction, (int, float)):
                                    prediction_value = prediction
                                else:
                                    prediction_value = 0.5
                                signal = 1 if prediction_value > 0.55 else (-1 if prediction_value < 0.45 else 0)
                            except Exception as e:
                                self.logger.warning(f"Error in deep learning prediction: {str(e)}")
                                signal = 0  # Hold by default

                        elif strategy == "Ensemble" and hasattr(self, 'advanced_predictor'):
                            # Use ensemble methods for signal generation
                            prediction = self.advanced_predictor.predict(symbol, data.iloc[:i])
                            signal = 1 if prediction > 0.55 else (-1 if prediction < 0.45 else 0)

                        else:
                            # Use simple moving average crossover strategy
                            if 'SMA_20' in data.columns and 'SMA_50' in data.columns:
                                sma_20 = data['SMA_20'].iloc[i]
                                sma_50 = data['SMA_50'].iloc[i]
                                prev_sma_20 = data['SMA_20'].iloc[i-1]
                                prev_sma_50 = data['SMA_50'].iloc[i-1]

                                # Buy signal: SMA 20 crosses above SMA 50
                                if prev_sma_20 <= prev_sma_50 and sma_20 > sma_50:
                                    signal = 1

                                # Sell signal: SMA 20 crosses below SMA 50
                                elif prev_sma_20 >= prev_sma_50 and sma_20 < sma_50:
                                    signal = -1

                        # Execute trades based on signal
                        if signal > 0 and position == 0:
                            # Buy
                            position = balance / current_price
                            entry_price = current_price
                            trades.append({
                                'date': data.index[i],
                                'type': 'buy',
                                'price': current_price,
                                'position': position,
                                'balance': balance
                            })

                        elif signal < 0 and position > 0:
                            # Sell
                            balance = position * current_price
                            profit = (current_price - entry_price) / entry_price
                            trades.append({
                                'date': data.index[i],
                                'type': 'sell',
                                'price': current_price,
                                'position': position,
                                'balance': balance,
                                'profit': profit
                            })
                            position = 0

                        # Update portfolio value
                        current_value = balance
                        if position > 0:
                            current_value = position * current_price
                        portfolio_values.append(current_value)

                    # Close any open position at the end
                    if position > 0:
                        balance = position * data['Close'].iloc[-1]
                        profit = (data['Close'].iloc[-1] - entry_price) / entry_price
                        trades.append({
                            'date': data.index[-1],
                            'type': 'sell',
                            'price': data['Close'].iloc[-1],
                            'position': position,
                            'balance': balance,
                            'profit': profit
                        })

                    # Calculate performance metrics
                    roi = (balance - self.initial_balance) / self.initial_balance

                    # Calculate win rate
                    win_trades = sum(1 for trade in trades if trade.get('type') == 'sell' and trade.get('profit', 0) > 0)
                    total_sell_trades = sum(1 for trade in trades if trade.get('type') == 'sell')
                    win_rate = win_trades / total_sell_trades if total_sell_trades > 0 else 0

                    # Calculate max drawdown
                    peak_balance = self.initial_balance
                    max_drawdown = 0

                    for trade in trades:
                        if trade.get('balance', 0) > peak_balance:
                            peak_balance = trade.get('balance', 0)

                        drawdown = (peak_balance - trade.get('balance', 0)) / peak_balance
                        max_drawdown = max(max_drawdown, drawdown)

                    # Calculate Sharpe ratio (simplified)
                    returns = []
                    for i in range(1, len(trades)):
                        if trades[i].get('type') == 'sell':
                            returns.append(trades[i].get('profit', 0))

                    sharpe_ratio = 0
                    if returns:
                        avg_return = sum(returns) / len(returns)
                        std_return = np.std(returns) if len(returns) > 1 else 1
                        sharpe_ratio = avg_return / std_return if std_return > 0 else 0

                    # Calculate confidence score
                    confidence = win_rate * (1 - max_drawdown) * (1 + sharpe_ratio) / 3

                    # Add simulation results to the list
                    all_results.append({
                        'symbol': symbol,
                        'strategy': strategy,
                        'initial_balance': self.initial_balance,
                        'final_balance': balance,
                        'roi': roi,
                        'win_rate': win_rate,
                        'max_drawdown': max_drawdown,
                        'sharpe_ratio': sharpe_ratio,
                        'trades': trades,
                        'trade_count': len(trades),
                        'portfolio_values': portfolio_values,
                        'confidence': confidence,
                        'status': 'success'
                    })

                    # Update learning models with simulation results
                    self._update_learning_from_simulation(symbol, strategy, all_results[-1])

                except Exception as e:
                    self.logger.error(f"Error running simulation for {symbol}: {str(e)}")
                    all_results.append({
                        'symbol': symbol,
                        'strategy': strategy,
                        'status': 'error',
                        'message': str(e)
                    })

            # Return all results if multiple symbols, or just the first result if a single symbol
            if len(all_results) == 1:
                return all_results[0]
            else:
                return all_results

        except Exception as e:
            self.logger.error(f"Error running simulation: {str(e)}")
            return {
                'status': 'error',
                'message': str(e)
            }

    def _update_learning_from_simulation(self, symbol, strategy, results):
        """
        Update learning models with simulation results

        Args:
            symbol: The symbol that was simulated
            strategy: The strategy that was used
            results: The simulation results
        """
        try:
            # Skip if simulation was not successful
            if results.get('status') != 'success':
                return

            # Update reinforcement learning model
            if strategy == "Reinforcement Learning" and hasattr(self, 'rl_trader') and self.rl_trader:
                try:
                    # Extract trades and performance metrics
                    trades = results.get('trades', [])
                    roi = results.get('roi', 0)
                    win_rate = results.get('win_rate', 0)

                    # Update RL model with simulation results
                    if hasattr(self.rl_trader, 'update_from_simulation'):
                        self.rl_trader.update_from_simulation(symbol, trades, roi, win_rate)

                    self.logger.info(f"Updated RL model for {symbol} with simulation results")
                except Exception as e:
                    self.logger.error(f"Error updating RL model for {symbol}: {str(e)}")

            # Update deep learning model
            if strategy == "Deep Learning" and hasattr(self, 'deep_learning_predictor') and self.deep_learning_predictor:
                try:
                    # Extract trades and performance metrics
                    trades = results.get('trades', [])

                    # Update deep learning model with simulation results
                    if hasattr(self.deep_learning_predictor, 'update_from_simulation'):
                        self.deep_learning_predictor.update_from_simulation(symbol, trades)

                    self.logger.info(f"Updated deep learning model for {symbol} with simulation results")
                except Exception as e:
                    self.logger.error(f"Error updating deep learning model for {symbol}: {str(e)}")

            # Update advanced predictor
            if strategy == "Ensemble" and hasattr(self, 'advanced_predictor') and self.advanced_predictor:
                try:
                    # Extract trades and performance metrics
                    trades = results.get('trades', [])

                    # Update advanced predictor with simulation results
                    if hasattr(self.advanced_predictor, 'update_from_simulation'):
                        self.advanced_predictor.update_from_simulation(symbol, trades)

                    self.logger.info(f"Updated advanced predictor for {symbol} with simulation results")
                except Exception as e:
                    self.logger.error(f"Error updating advanced predictor for {symbol}: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error updating learning from simulation: {str(e)}")

    def _calculate_indicators(self, data):
        """
        Calculate technical indicators for market data

        Args:
            data: DataFrame with market data

        Returns:
            dict: Dictionary of technical indicators
        """
        try:
            if data is None or data.empty or len(data) < 20:
                return {}

            # Create a copy to avoid modifying the original
            df = data.copy()

            # Initialize indicators dictionary
            indicators = {}

            # Simple Moving Averages
            indicators['SMA_5'] = df['Close'].rolling(window=5).mean()
            indicators['SMA_10'] = df['Close'].rolling(window=10).mean()
            indicators['SMA_20'] = df['Close'].rolling(window=20).mean()
            indicators['SMA_50'] = df['Close'].rolling(window=50).mean()
            indicators['SMA_200'] = df['Close'].rolling(window=200).mean()

            # Exponential Moving Averages
            indicators['EMA_12'] = df['Close'].ewm(span=12, adjust=False).mean()
            indicators['EMA_26'] = df['Close'].ewm(span=26, adjust=False).mean()

            # MACD
            indicators['MACD'] = indicators['EMA_12'] - indicators['EMA_26']
            indicators['Signal'] = indicators['MACD'].ewm(span=9, adjust=False).mean()
            indicators['MACD_Hist'] = indicators['MACD'] - indicators['Signal']

            # RSI
            delta = df['Close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=14).mean()
            avg_loss = loss.rolling(window=14).mean()
            rs = avg_gain / avg_loss
            indicators['RSI'] = 100 - (100 / (1 + rs))

            # Bollinger Bands
            indicators['BB_middle'] = df['Close'].rolling(window=20).mean()
            std_dev = df['Close'].rolling(window=20).std()
            indicators['BB_upper'] = indicators['BB_middle'] + (std_dev * 2)
            indicators['BB_lower'] = indicators['BB_middle'] - (std_dev * 2)

            # Stochastic Oscillator
            low_14 = df['Low'].rolling(window=14).min()
            high_14 = df['High'].rolling(window=14).max()
            indicators['Stoch_K'] = 100 * ((df['Close'] - low_14) / (high_14 - low_14))
            indicators['Stoch_D'] = indicators['Stoch_K'].rolling(window=3).mean()

            # Average True Range (ATR)
            tr1 = df['High'] - df['Low']
            tr2 = abs(df['High'] - df['Close'].shift())
            tr3 = abs(df['Low'] - df['Close'].shift())
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            indicators['ATR'] = tr.rolling(window=14).mean()

            # On-Balance Volume (OBV)
            obv = (np.sign(df['Close'].diff()) * df['Volume']).fillna(0).cumsum()
            indicators['OBV'] = obv

            # Percentage Price Oscillator (PPO)
            indicators['PPO'] = ((indicators['EMA_12'] - indicators['EMA_26']) / indicators['EMA_26']) * 100

            # Commodity Channel Index (CCI)
            tp = (df['High'] + df['Low'] + df['Close']) / 3
            tp_ma = tp.rolling(window=20).mean()
            tp_md = tp.rolling(window=20).apply(lambda x: np.mean(np.abs(x - np.mean(x))))
            indicators['CCI'] = (tp - tp_ma) / (0.015 * tp_md)

            # Williams %R
            highest_high = df['High'].rolling(window=14).max()
            lowest_low = df['Low'].rolling(window=14).min()
            indicators['Williams_R'] = -100 * ((highest_high - df['Close']) / (highest_high - lowest_low))

            # Rate of Change (ROC)
            indicators['ROC'] = df['Close'].pct_change(periods=10) * 100

            # Money Flow Index (MFI)
            tp = (df['High'] + df['Low'] + df['Close']) / 3
            money_flow = tp * df['Volume']
            pos_flow = money_flow.where(tp > tp.shift(), 0).rolling(window=14).sum()
            neg_flow = money_flow.where(tp < tp.shift(), 0).rolling(window=14).sum()
            money_ratio = pos_flow / neg_flow
            indicators['MFI'] = 100 - (100 / (1 + money_ratio))

            # Clean up NaN values
            for key in indicators:
                indicators[key] = indicators[key].fillna(0)

            return indicators

        except Exception as e:
            self.logger.error(f"Error calculating indicators: {str(e)}")
            return {}

    def _get_state_for_rl(self, data):
        """
        Convert market data to state representation for RL agent

        Args:
            data: DataFrame with market data

        Returns:
            numpy array: State representation
        """
        try:
            # Extract features for RL state
            close = data['Close'].values

            # Calculate returns
            returns = np.diff(close) / close[:-1]

            # Calculate technical indicators
            if len(close) > 14:
                # RSI
                delta = np.diff(close)
                gain = np.where(delta > 0, delta, 0)
                loss = np.where(delta < 0, -delta, 0)
                avg_gain = np.mean(gain[-14:])
                avg_loss = np.mean(loss[-14:])
                rs = avg_gain / avg_loss if avg_loss > 0 else 1
                rsi = 100 - (100 / (1 + rs))

                # Moving averages
                sma_5 = np.mean(close[-5:])
                sma_10 = np.mean(close[-10:])

                # Volatility
                volatility = np.std(returns[-14:])

                # Momentum
                momentum = close[-1] / close[-5] - 1

                # Combine features
                state = np.array([
                    returns[-1],
                    rsi / 100,
                    sma_5 / close[-1] - 1,
                    sma_10 / close[-1] - 1,
                    volatility,
                    momentum
                ])
            else:
                # Not enough data for indicators, use simple state
                state = np.array([0, 0.5, 0, 0, 0, 0])

            return state

        except Exception as e:
            self.logger.error(f"Error creating RL state: {str(e)}")
            return np.zeros(6)  # Return zero state on error

    def update(self):
        """
        Update the trading bot state

        This method is called periodically to update the trading bot state,
        including updating market data, checking positions, and executing trades.
        """
        try:
            self.logger.info("Updating trading bot state")

            # Update market data for watched symbols
            for symbol in self.watched_symbols:
                try:
                    # Get latest data
                    data = self.get_data(symbol)

                    # Update last price
                    if data is not None and not data.empty:
                        self.last_prices[symbol] = data['Close'].iloc[-1]

                    # Check positions for this symbol
                    if symbol in self.positions:
                        self.check_position(symbol, data)
                except Exception as e:
                    self.logger.error(f"Error updating {symbol}: {str(e)}")

            # Look for new trading opportunities
            self.scan_for_opportunities()

            # Update performance metrics
            self.update_performance_metrics()

            # Save state periodically
            self.save_state()

        except Exception as e:
            self.logger.error(f"Error in update: {str(e)}")

    def check_position(self, symbol: str, data: pd.DataFrame):
        """
        Check and update a position

        Args:
            symbol: The stock symbol
            data: DataFrame with OHLCV data and technical indicators
        """
        try:
            if symbol not in self.positions:
                return

            position = self.positions[symbol]
            entry_price = position['entry_price']
            current_price = data['Close'].iloc[-1]

            # Calculate profit/loss
            profit_loss_pct = (current_price - entry_price) / entry_price

            # Check stop loss
            if profit_loss_pct < -self.stop_loss:
                self.logger.info(f"Stop loss triggered for {symbol} at {profit_loss_pct:.2%}")
                self.sell_stock(symbol, position['shares'], current_price)
                return

            # Check take profit
            if profit_loss_pct > self.take_profit:
                self.logger.info(f"Take profit triggered for {symbol} at {profit_loss_pct:.2%}")
                self.sell_stock(symbol, position['shares'], current_price)
                return

            # Check trailing stop
            if symbol in self.highest_prices:
                highest_price = self.highest_prices[symbol]
                if current_price < highest_price * (1 - self.trailing_stop):
                    self.logger.info(f"Trailing stop triggered for {symbol} at {current_price:.2f} (highest: {highest_price:.2f})")
                    self.sell_stock(symbol, position['shares'], current_price)
                    return

            # Update highest price
            if symbol not in self.highest_prices or current_price > self.highest_prices[symbol]:
                self.highest_prices[symbol] = current_price

        except Exception as e:
            self.logger.error(f"Error checking position for {symbol}: {str(e)}")

    def scan_for_opportunities(self):
        """
        Scan for new trading opportunities
        """
        try:
            # Skip if we've reached the maximum number of positions
            if len(self.positions) >= self.max_positions:
                return

            # Get list of symbols to scan
            symbols_to_scan = self.watched_symbols.copy()

            # Remove symbols we already have positions in
            symbols_to_scan = [s for s in symbols_to_scan if s not in self.positions]

            # Limit the number of symbols to scan
            symbols_to_scan = symbols_to_scan[:self.max_symbols_limit]

            # Scan each symbol
            for symbol in symbols_to_scan:
                try:
                    data = self.get_data(symbol)
                    if data is None or data.empty:
                        continue

                    # Check if we should buy
                    if self.should_buy(symbol, data):
                        current_price = data['Close'].iloc[-1]

                        # Calculate position size
                        position_size = self.calculate_position_size(symbol, current_price)

                        # Buy the stock
                        self.buy_stock(symbol, position_size, current_price)
                except Exception as e:
                    self.logger.error(f"Error scanning {symbol}: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error scanning for opportunities: {str(e)}")

    def calculate_position_size(self, symbol: str, price: float) -> int:
        """
        Calculate the position size for a symbol

        Args:
            symbol: The stock symbol
            price: Current price of the symbol

        Returns:
            int: Number of shares to buy
        """
        try:
            # Calculate position size based on risk per trade
            max_position_value = self.balance * self.position_size_factor

            # Adjust position size based on market regime
            if HAS_ADVANCED_MODULES and self.market_regime_detector is not None:
                try:
                    data = self.get_data(symbol)
                    regime = self.market_regime_detector.detect_regime(data)

                    if regime == 'bullish':
                        # Increase position size in bullish regime
                        max_position_value *= 1.2
                    elif regime == 'bearish':
                        # Decrease position size in bearish regime
                        max_position_value *= 0.8
                except Exception as e:
                    self.logger.warning(f"Error detecting market regime for {symbol}: {str(e)}")

            # Calculate number of shares
            shares = int(max_position_value / price)

            # Ensure minimum position size
            if shares < 1:
                shares = 1

            return shares

        except Exception as e:
            self.logger.error(f"Error calculating position size for {symbol}: {str(e)}")
            return 1

    def update_performance_metrics(self):
        """
        Update performance metrics
        """
        try:
            # Calculate total value of positions
            total_position_value = 0
            for symbol, position in self.positions.items():
                if symbol in self.last_prices:
                    current_price = self.last_prices[symbol]
                    position_value = position['shares'] * current_price
                    total_position_value += position_value

            # Calculate total account value
            total_value = self.balance + total_position_value

            # Calculate daily profit/loss
            self.daily_profit = total_value - (self.initial_balance + self.total_profit)

            # Update total profit
            self.total_profit = total_value - self.initial_balance

            # Update max drawdown
            if self.total_profit < 0 and abs(self.total_profit) > self.max_drawdown:
                self.max_drawdown = abs(self.total_profit)

        except Exception as e:
            self.logger.error(f"Error updating performance metrics: {str(e)}")

    def save_state(self):
        """
        Save the current state of the trading bot
        """
        try:
            state = {
                'balance': self.balance,
                'positions': self.positions,
                'watched_symbols': self.watched_symbols,
                'last_prices': self.last_prices,
                'highest_prices': self.highest_prices,
                'trades_history': self.trades_history,
                'total_trades': self.total_trades,
                'winning_trades': self.winning_trades,
                'total_profit': self.total_profit,
                'daily_profit': self.daily_profit,
                'max_drawdown': self.max_drawdown,
                'timestamp': datetime.now().isoformat()
            }

            # Create config directory if it doesn't exist
            if not os.path.exists('config'):
                os.makedirs('config')

            # Save state to file
            with open('config/trading_bot_state.json', 'w') as f:
                json.dump(state, f, indent=4)

            self.logger.info("Saved trading bot state")

        except Exception as e:
            self.logger.error(f"Error saving state: {str(e)}")

    def load_state(self):
        """
        Load the saved state of the trading bot
        """
        try:
            if not os.path.exists('config/trading_bot_state.json'):
                self.logger.info("No saved state found")
                return

            with open('config/trading_bot_state.json', 'r') as f:
                state = json.load(f)

            self.balance = state.get('balance', self.initial_balance)
            self.positions = state.get('positions', {})
            self.watched_symbols = state.get('watched_symbols', [])
            self.last_prices = state.get('last_prices', {})
            self.highest_prices = state.get('highest_prices', {})
            self.trades_history = state.get('trades_history', [])
            self.total_trades = state.get('total_trades', 0)
            self.winning_trades = state.get('winning_trades', 0)
            self.total_profit = state.get('total_profit', 0.0)
            self.daily_profit = state.get('daily_profit', 0.0)
            self.max_drawdown = state.get('max_drawdown', 0.0)

            self.logger.info(f"Loaded trading bot state: {len(self.positions)} positions, {self.total_trades} trades")

        except Exception as e:
            self.logger.error(f"Error loading state: {str(e)}")

    def get_data(self, symbol: str, period: str = '1y', interval: str = '1d', start_date=None, end_date=None) -> pd.DataFrame:
        """
        Get historical data for a symbol

        Args:
            symbol: The stock symbol
            period: Time period to fetch (default: 1 year)
            interval: Data interval (default: 1 day)
            start_date: Start date for data (overrides period if provided)
            end_date: End date for data (overrides period if provided)

        Returns:
            DataFrame with OHLCV data
        """
        try:
            # Create cache key based on parameters
            if start_date and end_date:
                cache_key = f"{symbol}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}_{interval}"
            else:
                cache_key = f"{symbol}_{period}_{interval}"

            current_time = time.time()

            # Check cache first
            if cache_key in self.data_cache and current_time - self.last_cache_time.get(cache_key, 0) < self.cache_timeout:
                return self.data_cache[cache_key]

            # Fetch data from Yahoo Finance
            self.yahoo_request_count += 1
            if self.yahoo_request_count > self.YAHOO_DAILY_LIMIT:
                self.logger.warning(f"Yahoo Finance daily request limit reached ({self.YAHOO_DAILY_LIMIT})")
                return None

            ticker = yf.Ticker(symbol)

            # Use date range if provided, otherwise use period
            if start_date and end_date:
                data = ticker.history(start=start_date, end=end_date, interval=interval)
            else:
                data = ticker.history(period=period, interval=interval)

            # Add technical indicators
            if not data.empty:
                try:
                    # Check if we have enough data for indicators
                    if len(data) < 20:
                        self.logger.warning(f"Not enough data for technical indicators: {len(data)} rows")

                    # Use safe indicators if available
                    if HAS_SAFE_INDICATORS and len(data) >= 5:
                        # Use our safe implementation
                        data = add_safe_indicators(data, min_periods=1)
                    elif HAS_TA_PATCH and len(data) >= 5:
                        # Apply the patch to the ta library
                        original_methods = patch_ta_library()

                        try:
                            # Add SMA indicators
                            data['SMA_20'] = ta.trend.sma_indicator(data['Close'], window=20, fillna=True)
                            data['SMA_50'] = ta.trend.sma_indicator(data['Close'], window=50, fillna=True)
                            data['SMA_200'] = ta.trend.sma_indicator(data['Close'], window=200, fillna=True)

                            # Add RSI
                            data['RSI'] = ta.momentum.rsi(data['Close'], window=14, fillna=True)

                            # Add MACD
                            macd = ta.trend.MACD(data['Close'], fillna=True)
                            data['MACD'] = macd.macd()
                            data['MACD_signal'] = macd.macd_signal()
                            data['MACD_diff'] = macd.macd_diff()

                            # Add Bollinger Bands
                            bollinger = ta.volatility.BollingerBands(data['Close'], fillna=True)
                            bb_upper = bollinger.bollinger_hband()
                            bb_lower = bollinger.bollinger_lband()
                            bb_mid = bollinger.bollinger_mavg()

                            # Ensure we get Series, not DataFrame
                            data['BB_upper'] = bb_upper if isinstance(bb_upper, pd.Series) else bb_upper.iloc[:, 0] if hasattr(bb_upper, 'iloc') else pd.Series(bb_upper, index=data.index)
                            data['BB_lower'] = bb_lower if isinstance(bb_lower, pd.Series) else bb_lower.iloc[:, 0] if hasattr(bb_lower, 'iloc') else pd.Series(bb_lower, index=data.index)
                            data['BB_mid'] = bb_mid if isinstance(bb_mid, pd.Series) else bb_mid.iloc[:, 0] if hasattr(bb_mid, 'iloc') else pd.Series(bb_mid, index=data.index)

                            # Add ADX with patched library
                            data['ADX'] = ta.trend.adx(data['High'], data['Low'], data['Close'], window=14, fillna=True)

                            # Add DI+ and DI-
                            adx_indicator = ta.trend.ADXIndicator(
                                high=data['High'],
                                low=data['Low'],
                                close=data['Close'],
                                window=14,
                                fillna=True
                            )
                            data['DI+'] = adx_indicator.adx_pos()
                            data['DI-'] = adx_indicator.adx_neg()
                        finally:
                            # Restore the original methods
                            restore_ta_library(original_methods)
                    else:
                        # Fallback to standard ta library with try/except blocks
                        # Add SMA indicators
                        data['SMA_20'] = ta.trend.sma_indicator(data['Close'], window=20, fillna=True)
                        data['SMA_50'] = ta.trend.sma_indicator(data['Close'], window=50, fillna=True)
                        data['SMA_200'] = ta.trend.sma_indicator(data['Close'], window=200, fillna=True)

                        # Add RSI
                        data['RSI'] = ta.momentum.rsi(data['Close'], window=14, fillna=True)

                        # Add MACD
                        macd = ta.trend.MACD(data['Close'], fillna=True)
                        data['MACD'] = macd.macd()
                        data['MACD_signal'] = macd.macd_signal()
                        data['MACD_diff'] = macd.macd_diff()

                        # Add Bollinger Bands
                        bollinger = ta.volatility.BollingerBands(data['Close'], fillna=True)
                        bb_upper = bollinger.bollinger_hband()
                        bb_lower = bollinger.bollinger_lband()
                        bb_mid = bollinger.bollinger_mavg()

                        # Ensure we get Series, not DataFrame
                        data['BB_upper'] = bb_upper if isinstance(bb_upper, pd.Series) else bb_upper.iloc[:, 0] if hasattr(bb_upper, 'iloc') else pd.Series(bb_upper, index=data.index)
                        data['BB_lower'] = bb_lower if isinstance(bb_lower, pd.Series) else bb_lower.iloc[:, 0] if hasattr(bb_lower, 'iloc') else pd.Series(bb_lower, index=data.index)
                        data['BB_mid'] = bb_mid if isinstance(bb_mid, pd.Series) else bb_mid.iloc[:, 0] if hasattr(bb_mid, 'iloc') else pd.Series(bb_mid, index=data.index)

                        # Add ADX with safe handling
                        try:
                            if len(data) >= 14:
                                # Suppress warnings for this calculation
                                import warnings
                                with warnings.catch_warnings():
                                    warnings.simplefilter("ignore")
                                    data['ADX'] = ta.trend.adx(data['High'], data['Low'], data['Close'], fillna=True)
                            else:
                                data['ADX'] = pd.Series(np.nan, index=data.index).fillna(0)
                        except Exception as e:
                            self.logger.error(f"Error adding technical indicators: {str(e)}")
                            data['ADX'] = pd.Series(np.nan, index=data.index).fillna(0)
                except Exception as e:
                    self.logger.error(f"Error adding technical indicators: {str(e)}")
                    # Ensure we have the columns even if calculation fails
                    for col in ['SMA_20', 'SMA_50', 'SMA_200', 'RSI', 'MACD', 'MACD_signal',
                               'MACD_diff', 'BB_upper', 'BB_lower', 'BB_mid', 'ADX']:
                        if col not in data.columns:
                            data[col] = np.nan

                # Cache the data
                self.data_cache[cache_key] = data
                self.last_cache_time[cache_key] = current_time

            return data
        except Exception as e:
            self.logger.error(f"Error getting data for {symbol}: {str(e)}")
            return None

    def get_signal(self, symbol: str, data: pd.DataFrame) -> float:
        """
        Get a trading signal for a symbol

        Args:
            symbol: The stock symbol
            data: DataFrame with OHLCV data and technical indicators

        Returns:
            float: Signal value between -1.0 (strong sell) and 1.0 (strong buy)
        """
        try:
            # Make sure we have enough data
            if data is None or len(data) < 20:
                self.logger.warning(f"Not enough data for {symbol} to generate a signal")
                return 0.0

            # Get the latest data point
            latest = data.iloc[-1]

            # Technical indicator analysis
            buy_signals = 0
            sell_signals = 0
            total_signals = 0

            # 1. Moving Average Crossover
            if 'SMA_20' in latest and 'SMA_50' in latest and not pd.isna(latest['SMA_20']) and not pd.isna(latest['SMA_50']):
                total_signals += 1
                if latest['SMA_20'] > latest['SMA_50'] and data.iloc[-2]['SMA_20'] <= data.iloc[-2]['SMA_50']:
                    self.logger.info(f"{symbol} shows bullish MA crossover")
                    buy_signals += 1
                elif latest['SMA_20'] < latest['SMA_50'] and data.iloc[-2]['SMA_20'] >= data.iloc[-2]['SMA_50']:
                    self.logger.info(f"{symbol} shows bearish MA crossover")
                    sell_signals += 1

            # 2. RSI (Relative Strength Index)
            if 'RSI' in latest and not pd.isna(latest['RSI']):
                total_signals += 1
                if latest['RSI'] < 30:
                    # Oversold condition, strong buy signal
                    self.logger.info(f"{symbol} is oversold (RSI: {latest['RSI']:.2f})")
                    buy_signals += 1
                elif latest['RSI'] > 70:
                    # Overbought condition, strong sell signal
                    self.logger.info(f"{symbol} is overbought (RSI: {latest['RSI']:.2f})")
                    sell_signals += 1

            # 3. MACD (Moving Average Convergence Divergence)
            if 'MACD' in latest and not pd.isna(latest['MACD']):
                total_signals += 1
                if latest['MACD'] > 0 and data.iloc[-2]['MACD'] <= 0:
                    self.logger.info(f"{symbol} shows bullish MACD crossover")
                    buy_signals += 1
                elif latest['MACD'] < 0 and data.iloc[-2]['MACD'] >= 0:
                    self.logger.info(f"{symbol} shows bearish MACD crossover")
                    sell_signals += 1

            # Advanced prediction systems
            if HAS_ADVANCED_MODULES:
                # 4. Deep Learning Prediction
                if self.deep_learning_predictor is not None:
                    try:
                        prediction = self.deep_learning_predictor.predict(symbol, data)
                        if prediction and 'predicted_return' in prediction:
                            total_signals += 1
                            if prediction['predicted_return'] > 0.01:
                                self.logger.info(f"{symbol} deep learning predicts {prediction['predicted_return']:.2%} return")
                                buy_signals += 1
                            elif prediction['predicted_return'] < -0.01:
                                self.logger.info(f"{symbol} deep learning predicts {prediction['predicted_return']:.2%} return")
                                sell_signals += 1
                    except Exception as e:
                        self.logger.warning(f"Error getting deep learning prediction for {symbol}: {str(e)}")

                # 5. Reinforcement Learning Agent
                if self.rl_trader is not None:
                    try:
                        rl_signal = self.rl_trader.get_signal(symbol, data)
                        total_signals += 1
                        if rl_signal > 0.2:
                            buy_signals += 1
                        elif rl_signal < -0.2:
                            sell_signals += 1
                    except Exception as e:
                        self.logger.warning(f"Error getting RL signal for {symbol}: {str(e)}")

                # 6. Quantum Agent
                if self.use_quantum_agent and self.quantum_agent is not None:
                    try:
                        quantum_signal = self.quantum_agent.get_signal(symbol, data)
                        total_signals += 1
                        if quantum_signal > 0.6:  # Strong buy signal
                            self.logger.info(f"{symbol} quantum agent signals buy ({quantum_signal:.2f})")
                            buy_signals += 1
                        elif quantum_signal < -0.6:  # Strong sell signal
                            self.logger.info(f"{symbol} quantum agent signals sell ({quantum_signal:.2f})")
                            sell_signals += 1
                    except Exception as e:
                        self.logger.warning(f"Error getting quantum signal for {symbol}: {str(e)}")

            # Calculate signal strength (-1.0 to 1.0)
            if total_signals == 0:
                return 0.0

            signal = (buy_signals - sell_signals) / total_signals
            self.logger.info(f"{symbol} signal: {signal:.2f} ({buy_signals} buy, {sell_signals} sell, {total_signals} total)")

            return max(-1.0, min(1.0, signal))  # Clamp between -1.0 and 1.0

        except Exception as e:
            self.logger.error(f"Error in get_signal for {symbol}: {str(e)}")
            return 0.0

    def should_buy(self, symbol: str, data: pd.DataFrame) -> bool:
        """
        Determine if we should buy a stock based on technical indicators and prediction models

        Args:
            symbol: The stock symbol
            data: DataFrame with OHLCV data and technical indicators

        Returns:
            True if we should buy, False otherwise
        """
        try:
            # Check if we already have a position in this stock
            if symbol in self.positions:
                self.logger.info(f"Already have a position in {symbol}, not buying more")
                return False

            # Check if we've reached the maximum number of positions
            if len(self.positions) >= self.max_positions:
                self.logger.info(f"Maximum number of positions reached ({self.max_positions}), not buying {symbol}")
                return False

            # Make sure we have enough data
            if data is None or len(data) < 20:
                self.logger.warning(f"Not enough data for {symbol} to make a buy decision")
                return False

            # Get the latest data point
            latest = data.iloc[-1]

            # Check minimum price and volume requirements
            if 'Close' not in latest or 'Volume' not in latest:
                self.logger.warning(f"Missing required data for {symbol}")
                return False

            current_price = latest['Close']
            current_volume = latest['Volume']

            if current_price < self.min_price:
                self.logger.info(f"{symbol} price ({current_price}) below minimum ({self.min_price})")
                return False

            if current_volume < self.min_volume:
                self.logger.info(f"{symbol} volume ({current_volume}) below minimum ({self.min_volume})")
                return False

            # Technical indicator analysis
            buy_signals = 0
            total_signals = 0

            # 1. Moving Average Crossover
            if 'SMA_20' in latest and 'SMA_50' in latest and not pd.isna(latest['SMA_20']) and not pd.isna(latest['SMA_50']):
                total_signals += 1
                if latest['SMA_20'] > latest['SMA_50'] and data.iloc[-2]['SMA_20'] <= data.iloc[-2]['SMA_50']:
                    self.logger.info(f"{symbol} shows bullish MA crossover")
                    buy_signals += 1

            # 2. RSI (Relative Strength Index)
            if 'RSI' in latest and not pd.isna(latest['RSI']):
                total_signals += 1
                if latest['RSI'] > 30 and latest['RSI'] < 70:
                    # RSI in healthy range, not overbought or oversold
                    buy_signals += 1
                if latest['RSI'] < 30:
                    # Oversold condition, strong buy signal
                    self.logger.info(f"{symbol} is oversold (RSI: {latest['RSI']:.2f})")
                    buy_signals += 2

            # 3. MACD (Moving Average Convergence Divergence)
            if 'MACD' in latest and not pd.isna(latest['MACD']):
                total_signals += 1
                if latest['MACD'] > 0 and data.iloc[-2]['MACD'] <= 0:
                    self.logger.info(f"{symbol} shows bullish MACD crossover")
                    buy_signals += 1

            # 4. Price above moving averages
            if 'SMA_20' in latest and 'SMA_50' in latest and not pd.isna(latest['SMA_20']) and not pd.isna(latest['SMA_50']):
                total_signals += 1
                if current_price > latest['SMA_20'] and current_price > latest['SMA_50']:
                    buy_signals += 1

            # 5. ADX (Average Directional Index) for trend strength
            if 'ADX' in latest and not pd.isna(latest['ADX']):
                total_signals += 1
                if latest['ADX'] > 25:  # Strong trend
                    buy_signals += 1

            # 6. Bollinger Bands
            if 'BB_lower' in latest and not pd.isna(latest['BB_lower']):
                total_signals += 1
                if current_price < latest['BB_lower'] * 1.02:  # Price near lower band
                    self.logger.info(f"{symbol} near lower Bollinger Band")
                    buy_signals += 1

            # Advanced prediction systems
            if HAS_ADVANCED_MODULES:
                # 7. Deep Learning Prediction
                if self.deep_learning_predictor is not None:
                    try:
                        prediction = self.deep_learning_predictor.predict(symbol, data)
                        if prediction and 'predicted_return' in prediction and prediction['predicted_return'] > 0.01:
                            self.logger.info(f"{symbol} deep learning predicts {prediction['predicted_return']:.2%} return")
                            buy_signals += 2
                            total_signals += 2
                    except Exception as e:
                        self.logger.warning(f"Error getting deep learning prediction for {symbol}: {str(e)}")

                # 8. Quantum Agent
                if self.use_quantum_agent and self.quantum_agent is not None:
                    try:
                        quantum_signal = self.quantum_agent.get_signal(symbol, data)
                        if quantum_signal > 0.6:  # Strong buy signal
                            self.logger.info(f"{symbol} quantum agent signals buy ({quantum_signal:.2f})")
                            buy_signals += 2
                            total_signals += 2
                    except Exception as e:
                        self.logger.warning(f"Error getting quantum signal for {symbol}: {str(e)}")

                # 9. Market Regime
                if self.market_regime_detector is not None:
                    try:
                        regime = self.market_regime_detector.detect_regime(data)
                        if regime == 'bullish':
                            buy_signals += 1
                            total_signals += 1
                    except Exception as e:
                        self.logger.warning(f"Error detecting market regime for {symbol}: {str(e)}")

            # Calculate buy score
            buy_score = buy_signals / max(1, total_signals)
            self.logger.info(f"{symbol} buy score: {buy_score:.2f} ({buy_signals}/{total_signals} signals)")

            # Decision threshold
            return buy_score >= 0.6  # At least 60% of signals should be positive

        except Exception as e:
            self.logger.error(f"Error in should_buy for {symbol}: {str(e)}")
            return False

    def buy_stock(self, symbol: str, shares: int, price: float) -> bool:
        """
        Buy a stock and add it to positions

        Args:
            symbol: The stock symbol
            shares: Number of shares to buy
            price: Purchase price per share

        Returns:
            True if the purchase was successful, False otherwise
        """
        try:
            # Check if we already have a position in this stock
            if symbol in self.positions:
                self.logger.info(f"Already have a position in {symbol}, not buying more")
                return False

            # Check if we've reached the maximum number of positions
            if len(self.positions) >= self.max_positions:
                self.logger.info(f"Maximum number of positions reached ({self.max_positions}), not buying {symbol}")
                return False

            # Calculate total cost
            total_cost = shares * price

            # Check if we have enough balance
            if total_cost > self.balance:
                self.logger.info(f"Not enough balance to buy {shares} shares of {symbol} at ${price:.2f} (total: ${total_cost:.2f})")
                return False

            # Add position
            self.positions[symbol] = {
                'shares': shares,
                'entry_price': price,
                'entry_time': datetime.now(),
                'stop_loss': price * (1 - self.stop_loss),
                'take_profit': price * (1 + self.take_profit),
                'trailing_stop': price * (1 - self.trailing_stop),
                'highest_price': price
            }

            # Update balance
            self.balance -= total_cost

            # Record trade in history
            self.trades_history.append({
                'symbol': symbol,
                'action': 'buy',
                'shares': shares,
                'price': price,
                'total': total_cost,
                'time': datetime.now(),
                'balance_after': self.balance
            })

            # Update last price
            self.last_prices[symbol] = price
            self.highest_prices[symbol] = price

            # Log the trade
            self.logger.info(f"Bought {shares} shares of {symbol} at ${price:.2f} (total: ${total_cost:.2f})")

            # Notify advanced components about the trade
            self._notify_trade_event(symbol, 'buy', shares, price)

            return True

        except Exception as e:
            self.logger.error(f"Error buying {symbol}: {str(e)}")
            return False

    def sell_stock(self, symbol: str, price: float, reason: str = "manual") -> bool:
        """
        Sell a stock and remove it from positions

        Args:
            symbol: The stock symbol
            price: Selling price per share
            reason: Reason for selling (e.g., "stop_loss", "take_profit", "trailing_stop", "manual")

        Returns:
            True if the sale was successful, False otherwise
        """
        try:
            # Check if we have a position in this stock
            if symbol not in self.positions:
                self.logger.info(f"No position in {symbol}, cannot sell")
                return False

            # Get position details
            position = self.positions[symbol]
            shares = position['shares']
            entry_price = position['entry_price']
            entry_time = position['entry_time']

            # Calculate total proceeds
            total_proceeds = shares * price

            # Calculate profit/loss
            profit_loss = total_proceeds - (shares * entry_price)
            profit_loss_pct = (price / entry_price - 1) * 100

            # Update balance
            self.balance += total_proceeds

            # Record trade in history
            self.trades_history.append({
                'symbol': symbol,
                'action': 'sell',
                'shares': shares,
                'price': price,
                'total': total_proceeds,
                'profit_loss': profit_loss,
                'profit_loss_pct': profit_loss_pct,
                'time': datetime.now(),
                'balance_after': self.balance,
                'reason': reason
            })

            # Update performance metrics
            if profit_loss > 0:
                self.winning_trades += 1
            self.total_trades += 1
            self.total_profit += profit_loss

            # Remove position
            del self.positions[symbol]

            # Log the trade
            self.logger.info(f"Sold {shares} shares of {symbol} at ${price:.2f} (total: ${total_proceeds:.2f}, P/L: ${profit_loss:.2f}, {profit_loss_pct:.2f}%)")

            # Notify advanced components about the trade
            self._notify_trade_event(symbol, 'sell', shares, price, profit_loss, profit_loss_pct, reason)

            return True

        except Exception as e:
            self.logger.error(f"Error selling {symbol}: {str(e)}")
            return False

    def _notify_trade_event(self, symbol: str, action: str, shares: int, price: float, profit_loss: float = 0, profit_loss_pct: float = 0, reason: str = None):
        """
        Notify advanced components about a trade event

        Args:
            symbol: The stock symbol
            action: Trade action ('buy' or 'sell')
            shares: Number of shares
            price: Trade price per share
            profit_loss: Profit or loss amount (for sells)
            profit_loss_pct: Profit or loss percentage (for sells)
            reason: Reason for selling (for sells)
        """
        try:
            # Notify continuous learning engine if available
            if hasattr(self, 'continuous_learning_engine') and self.continuous_learning_engine is not None:
                try:
                    self.continuous_learning_engine.on_trade_event({
                        'symbol': symbol,
                        'action': action,
                        'shares': shares,
                        'price': price,
                        'profit_loss': profit_loss,
                        'profit_loss_pct': profit_loss_pct,
                        'reason': reason,
                        'time': datetime.now().isoformat()
                    })
                except Exception as e:
                    self.logger.warning(f"Error notifying continuous learning engine: {str(e)}")

            # Notify memory learning if available
            if hasattr(self, 'memory_learning') and self.memory_learning is not None:
                try:
                    self.memory_learning.record_trade({
                        'symbol': symbol,
                        'action': action,
                        'shares': shares,
                        'price': price,
                        'profit_loss': profit_loss,
                        'profit_loss_pct': profit_loss_pct,
                        'reason': reason,
                        'time': datetime.now().isoformat()
                    })
                except Exception as e:
                    self.logger.warning(f"Error notifying memory learning: {str(e)}")

            # Notify adaptive RL agent if available
            if hasattr(self, 'adaptive_rl') and self.adaptive_rl is not None:
                try:
                    self.adaptive_rl.on_trade_event(symbol, action, price, profit_loss_pct)
                except Exception as e:
                    self.logger.warning(f"Error notifying adaptive RL agent: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error in _notify_trade_event: {str(e)}")

    def add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators to dataframe"""
        try:
            # Make sure we have enough data
            if len(df) < 50:
                self.logger.warning(f"Not enough data for technical indicators: {len(df)} rows")
                # Still try to calculate with what we have, but some indicators might be NaN
                # This is better than returning no indicators at all

            # Handle different data formats
            # First, ensure we have a proper DataFrame
            if not isinstance(df, pd.DataFrame):
                self.logger.warning("Input is not a DataFrame, attempting to convert")
                df = pd.DataFrame(df)

            # Ensure we have the required columns
            required_columns = ['Close', 'High', 'Low']
            for col in required_columns:
                if col not in df.columns:
                    self.logger.warning(f"Missing required column: {col}")
                    return df

            # Extract Series and ensure they are 1D
            close_series = df['Close']
            high_series = df['High']
            low_series = df['Low']

            # Handle multi-dimensional arrays (convert to 1D)
            if hasattr(close_series, 'values') and len(close_series.values.shape) > 1:
                self.logger.info(f"Converting multi-dimensional Close series to 1D. Shape: {close_series.values.shape}")
                close_series = pd.Series(close_series.values.flatten())

            if hasattr(high_series, 'values') and len(high_series.values.shape) > 1:
                self.logger.info(f"Converting multi-dimensional High series to 1D. Shape: {high_series.values.shape}")
                high_series = pd.Series(high_series.values.flatten())

            if hasattr(low_series, 'values') and len(low_series.values.shape) > 1:
                self.logger.info(f"Converting multi-dimensional Low series to 1D. Shape: {low_series.values.shape}")
                low_series = pd.Series(low_series.values.flatten())

            # Price-based indicators
            df['SMA_20'] = ta.trend.sma_indicator(close_series, window=20)
            df['SMA_50'] = ta.trend.sma_indicator(close_series, window=50)
            df['EMA_12'] = ta.trend.ema_indicator(close_series, window=12)
            df['EMA_26'] = ta.trend.ema_indicator(close_series, window=26)

            # Momentum indicators
            df['RSI'] = ta.momentum.rsi(close_series, window=14)
            df['MACD'] = ta.trend.macd_diff(close_series)
            df['ADX'] = ta.trend.adx(high_series, low_series, close_series)

            # Volatility indicators
            df['ATR'] = ta.volatility.average_true_range(high_series, low_series, close_series)
            df['BB_upper'] = ta.volatility.bollinger_hband(close_series)
            df['BB_lower'] = ta.volatility.bollinger_lband(close_series)

            return df
        except Exception as e:
            self.logger.error(f"Error adding technical indicators: {str(e)}")
            return df

    def get_alpha_vantage_data(self, symbol: str, interval: str = '1d') -> Optional[pd.DataFrame]:
        """Get stock data from Alpha Vantage API with interval support"""
        try:
            if not ALPHA_VANTAGE_API_KEY:
                self.logger.warning("Alpha Vantage API key not found")
                return None

            # Map interval to Alpha Vantage format
            av_interval_map = {
                '1m': 'TIME_SERIES_INTRADAY&interval=1min',
                '5m': 'TIME_SERIES_INTRADAY&interval=5min',
                '15m': 'TIME_SERIES_INTRADAY&interval=15min',
                '30m': 'TIME_SERIES_INTRADAY&interval=30min',
                '60m': 'TIME_SERIES_INTRADAY&interval=60min',
                '1h': 'TIME_SERIES_INTRADAY&interval=60min',
                '1d': 'TIME_SERIES_DAILY_ADJUSTED',
                'daily': 'TIME_SERIES_DAILY_ADJUSTED',
                'weekly': 'TIME_SERIES_WEEKLY_ADJUSTED',
                'monthly': 'TIME_SERIES_MONTHLY_ADJUSTED'
            }

            # Default to daily if interval not supported
            av_function = av_interval_map.get(interval, 'TIME_SERIES_DAILY_ADJUSTED')

            self.logger.info(f"Fetching {symbol} data from Alpha Vantage with interval {interval}")

            # Build URL based on interval
            if 'INTRADAY' in av_function:
                url = f"https://www.alphavantage.co/query?function={av_function}&symbol={symbol}&outputsize=full&apikey={ALPHA_VANTAGE_API_KEY}"
            else:
                url = f"https://www.alphavantage.co/query?function={av_function}&symbol={symbol}&outputsize=full&apikey={ALPHA_VANTAGE_API_KEY}"

            response = requests.get(url)
            data = response.json()

            # Determine the time series key based on the function
            time_series_key = None
            if 'TIME_SERIES_INTRADAY&interval=1min' in av_function:
                time_series_key = 'Time Series (1min)'
            elif 'TIME_SERIES_INTRADAY&interval=5min' in av_function:
                time_series_key = 'Time Series (5min)'
            elif 'TIME_SERIES_INTRADAY&interval=15min' in av_function:
                time_series_key = 'Time Series (15min)'
            elif 'TIME_SERIES_INTRADAY&interval=30min' in av_function:
                time_series_key = 'Time Series (30min)'
            elif 'TIME_SERIES_INTRADAY&interval=60min' in av_function:
                time_series_key = 'Time Series (60min)'
            elif 'TIME_SERIES_DAILY' in av_function:
                time_series_key = 'Time Series (Daily)'
            elif 'TIME_SERIES_WEEKLY' in av_function:
                time_series_key = 'Weekly Adjusted Time Series'
            elif 'TIME_SERIES_MONTHLY' in av_function:
                time_series_key = 'Monthly Adjusted Time Series'
            else:
                time_series_key = 'Time Series (Daily)'  # Default

            # Check if we have the expected time series key
            if time_series_key not in data:
                self.logger.error(f"Invalid data received from Alpha Vantage for {symbol} with interval {interval}: {data}")
                return None

            # Convert to DataFrame
            time_series = data[time_series_key]
            df = pd.DataFrame.from_dict(time_series, orient='index')
            df.index = pd.DatetimeIndex(df.index)
            df = df.sort_index(ascending=True)

            # Rename columns - handle different formats for different intervals
            if 'Time Series' in time_series_key and 'min' in time_series_key:
                # Intraday data format
                df = df.rename(columns={
                    '1. open': 'Open',
                    '2. high': 'High',
                    '3. low': 'Low',
                    '4. close': 'Close',
                    '5. volume': 'Volume'
                })
                # Add Adj Close (same as Close for intraday data)
                df['Adj Close'] = df['Close']
            else:
                # Daily, weekly, monthly data format
                df = df.rename(columns={
                    '1. open': 'Open',
                    '2. high': 'High',
                    '3. low': 'Low',
                    '4. close': 'Close',
                    '5. adjusted close': 'Adj Close',
                    '6. volume': 'Volume'
                })

            # Convert numeric columns
            for col in df.columns:
                df[col] = pd.to_numeric(df[col])

            # Extract last year's data
            df = df.tail(365)

            # Add technical indicators
            df = self.add_technical_indicators(df)

            return df

        except Exception as e:
            self.logger.error(f"Error fetching Alpha Vantage data for {symbol}: {str(e)}")
            return None

    def get_polygon_data(self, symbol: str, interval: str = '1d') -> Optional[pd.DataFrame]:
        """Get stock data from Polygon API with interval support"""
        try:
            if not POLYGON_API_KEY:
                self.logger.warning("Polygon API key not found")
                return None

            self.logger.info(f"Fetching {symbol} data from Polygon with interval {interval}")

            # Map interval to Polygon format
            polygon_multiplier = 1
            polygon_timespan = 'day'

            if interval == '1m':
                polygon_multiplier = 1
                polygon_timespan = 'minute'
            elif interval == '5m':
                polygon_multiplier = 5
                polygon_timespan = 'minute'
            elif interval == '15m':
                polygon_multiplier = 15
                polygon_timespan = 'minute'
            elif interval == '30m':
                polygon_multiplier = 30
                polygon_timespan = 'minute'
            elif interval == '60m' or interval == '1h':
                polygon_multiplier = 1
                polygon_timespan = 'hour'
            elif interval == '4h':
                polygon_multiplier = 4
                polygon_timespan = 'hour'
            elif interval == '1d' or interval == 'daily':
                polygon_multiplier = 1
                polygon_timespan = 'day'
            elif interval == 'weekly' or interval == '1wk':
                polygon_multiplier = 1
                polygon_timespan = 'week'
            elif interval == 'monthly' or interval == '1mo':
                polygon_multiplier = 1
                polygon_timespan = 'month'

            # Adjust timespan based on interval
            end_date = datetime.now()

            if polygon_timespan == 'minute' or polygon_timespan == 'hour':
                # For intraday data, use shorter timespan
                start_date = end_date - timedelta(days=7)
            else:
                # For daily or longer, use 1 year
                start_date = end_date - timedelta(days=365)

            url = f"https://api.polygon.io/v2/aggs/ticker/{symbol}/range/{polygon_multiplier}/{polygon_timespan}/{start_date.strftime('%Y-%m-%d')}/{end_date.strftime('%Y-%m-%d')}?apiKey={POLYGON_API_KEY}"

            response = requests.get(url)
            data = response.json()

            if 'results' not in data:
                self.logger.error(f"Invalid data received from Polygon for {symbol}: {data}")
                return None

            # Convert to DataFrame
            results = data['results']
            df = pd.DataFrame(results)

            if df.empty:
                self.logger.error(f"Empty data received from Polygon for {symbol}")
                return None

            # Convert timestamp to datetime
            df['t'] = pd.to_datetime(df['t'], unit='ms')
            df = df.set_index('t')

            # Rename columns
            df = df.rename(columns={
                'o': 'Open',
                'h': 'High',
                'l': 'Low',
                'c': 'Close',
                'v': 'Volume'
            })

            # Add Adj Close (same as Close for this source)
            df['Adj Close'] = df['Close']

            # Add technical indicators
            df = self.add_technical_indicators(df)

            return df

        except Exception as e:
            self.logger.error(f"Error fetching Polygon data for {symbol}: {str(e)}")
            return None

    def download_data(self, symbol: str, period: str = '6mo', interval: str = '1d') -> pd.DataFrame:
        """Download data with support for different timeframes

        Args:
            symbol: The stock symbol
            period: The time period ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
            interval: The data interval ('1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo')

        Returns:
            DataFrame with OHLCV data and technical indicators
        """
        max_retries = 3
        current_time = datetime.now()

        # Create a cache key that includes the interval
        cache_key = f"{symbol}_{period}_{interval}"

        try:
            # Check if we have cached data for this symbol, period, and interval
            if cache_key in self.data_cache and cache_key in self.last_cache_time:
                cache_time = self.last_cache_time[cache_key]
                # Use cached data if it's less than 5 minutes old
                if (current_time - cache_time).total_seconds() < 300:  # 5 minutes
                    return self.data_cache[cache_key]

            # Check if this is a cryptocurrency symbol
            is_crypto = self._is_crypto_symbol(symbol)

            if is_crypto:
                # Use our data provider for cryptocurrencies
                self.logger.info(f"Using crypto exchanges for {symbol} data with interval={interval}")

                # Convert period to start and end dates
                end_date = datetime.now()
                if period == '1d':
                    start_date = end_date - timedelta(days=1)
                elif period == '5d':
                    start_date = end_date - timedelta(days=5)
                elif period == '1mo':
                    start_date = end_date - timedelta(days=30)
                elif period == '3mo':
                    start_date = end_date - timedelta(days=90)
                elif period == '6mo':
                    start_date = end_date - timedelta(days=180)
                elif period == '1y':
                    start_date = end_date - timedelta(days=365)
                elif period == '2y':
                    start_date = end_date - timedelta(days=730)
                elif period == '5y':
                    start_date = end_date - timedelta(days=1825)
                else:  # Default to 6 months
                    start_date = end_date - timedelta(days=180)

                # Use the data provider to get data
                from data_provider import DataProvider

                # Check if we're in live trading mode
                live_trading_mode = self._get_env_var('TRADING_BOT_LIVE_MODE', False)

                # Initialize data provider with live trading mode flag
                data_provider = DataProvider(live_trading_mode=live_trading_mode)

                if live_trading_mode:
                    self.logger.warning(f"Getting data for {symbol} in LIVE TRADING mode - synthetic data will NOT be used")

                df = data_provider.get_data(symbol, start_date, end_date, interval)

                if df is not None and not df.empty:
                    # Add technical indicators
                    df = self.add_technical_indicators(df)

                    # Cache the data
                    self.data_cache[cache_key] = df
                    self.last_cache_time[cache_key] = current_time

                    return df
                else:
                    self.logger.warning(f"Could not get crypto data for {symbol} from data provider, falling back to other sources")
            else:
                # For non-crypto symbols, use the original sources
                self.logger.info(f"Using traditional data sources for {symbol}")

            # Try multiple times in case of API rate limiting
            for _ in range(max_retries):
                try:
                    # Use Yahoo Finance with interval support
                    if self.yahoo_request_count < self.YAHOO_DAILY_LIMIT:
                        self.logger.info(f"Downloading {symbol} data with period={period}, interval={interval}")
                        df = yf.download(symbol, period=period, interval=interval, progress=False)
                        if df is not None and not df.empty:
                            self.yahoo_request_count += 1

                            # Add technical indicators
                            df = self.add_technical_indicators(df)

                            # Cache the data with the new cache key
                            self.data_cache[cache_key] = df
                            self.last_cache_time[cache_key] = current_time

                            return df
                    else:
                        self.logger.warning(f"Yahoo Finance daily request limit reached, using alternative APIs")
                except Exception as e:
                    if "Too Many Requests" in str(e):
                        self.logger.warning(f"Yahoo Finance rate limited, switching to alternative APIs")
                    else:
                        self.logger.error(f"Error from Yahoo Finance for {symbol}: {str(e)}")

                # Try Alpha Vantage with interval support
                df = self.get_alpha_vantage_data(symbol, interval=interval)
                if df is not None and not df.empty:
                    # Add technical indicators
                    df = self.add_technical_indicators(df)

                    # Cache the data with the new cache key
                    self.data_cache[cache_key] = df
                    self.last_cache_time[cache_key] = current_time
                    return df

                # Try Polygon as a last resort with interval support
                df = self.get_polygon_data(symbol, interval=interval)
                if df is not None and not df.empty:
                    # Add technical indicators
                    df = self.add_technical_indicators(df)

                    # Cache the data with the new cache key
                    self.data_cache[cache_key] = df
                    self.last_cache_time[cache_key] = current_time
                    return df

            self.logger.error(f"Failed to get data for {symbol} from all sources")
            return None

        except Exception as e:
            self.logger.error(f"Error downloading data for {symbol}: {str(e)}")
            return None

    def _is_crypto_symbol(self, symbol):
        """Check if a symbol is a cryptocurrency"""
        # Common patterns for crypto symbols
        crypto_patterns = [
            '-USD', '-USDT', '-EUR', '-BTC', '-ETH',  # Dash format (common in exchanges)
            '/USD', '/USDT', '/EUR', '/BTC', '/ETH',  # Slash format
            'BTC', 'ETH', 'XRP', 'LTC', 'BCH', 'ADA', 'DOT', 'LINK', 'XLM', 'DOGE',  # Common cryptos
            'UNI', 'AAVE', 'SNX', 'COMP', 'YFI', 'MKR', 'SUSHI', '1INCH'  # DeFi tokens
        ]

        # Check if symbol matches any crypto pattern
        symbol_upper = symbol.upper()
        for pattern in crypto_patterns:
            if pattern in symbol_upper:
                return True

        return False

    def analyze_stock(self, symbol: str, data: pd.DataFrame) -> float:
        """Advanced stock analysis combining multiple factors"""
        try:
            if data is None or len(data) < 20:
                return 0.0

            latest = data.iloc[-1]
            score = 0.0

            # Check if we have the required technical indicators
            missing_indicators = []
            for indicator in ['SMA_20', 'SMA_50', 'RSI', 'ATR', 'ADX']:
                if indicator not in latest:
                    missing_indicators.append(indicator)

            if missing_indicators:
                self.logger.warning(f"Missing technical indicators for {symbol}: {', '.join(missing_indicators)}")
                # Continue with the indicators we have, use default values for missing ones

            # Get values, handling Series objects and missing indicators
            close_price = latest['Close']
            sma_20 = latest['SMA_20'] if 'SMA_20' in latest else close_price
            sma_50 = latest['SMA_50'] if 'SMA_50' in latest else close_price * 0.95  # Default to 5% below close
            rsi = latest['RSI'] if 'RSI' in latest else 50  # Default to neutral RSI
            atr = latest['ATR'] if 'ATR' in latest else close_price * 0.02  # Default to 2% volatility
            adx = latest['ADX'] if 'ADX' in latest else 20  # Default to moderate trend strength

            # Convert pandas Series to scalar if needed
            if hasattr(close_price, 'iloc') and len(close_price) > 0:
                close_price = close_price.iloc[-1]
            if hasattr(sma_20, 'iloc') and len(sma_20) > 0:
                sma_20 = sma_20.iloc[-1]
            if hasattr(sma_50, 'iloc') and len(sma_50) > 0:
                sma_50 = sma_50.iloc[-1]
            if hasattr(rsi, 'iloc') and len(rsi) > 0:
                rsi = rsi.iloc[-1]
            if hasattr(atr, 'iloc') and len(atr) > 0:
                atr = atr.iloc[-1]
            if hasattr(adx, 'iloc') and len(adx) > 0:
                adx = adx.iloc[-1]

            # 1. Technical Analysis (40% weight)
            tech_score = 0.0

            # Trend analysis
            if close_price > sma_20 > sma_50:
                tech_score += 0.3

            # Momentum
            if rsi < 30:  # Oversold
                tech_score += 0.2
            elif rsi < 70:  # Not overbought
                tech_score += 0.1

            # Volatility
            if atr / close_price < self.max_volatility:
                tech_score += 0.1

            # ADX trend strength
            if adx > 25:
                tech_score += 0.2

            score += tech_score * 0.4

            # 2. Market Microstructure (20% weight)
            if self.microstructure_analyzer is not None:
                try:
                    microstructure_result = self.microstructure_analyzer.analyze(symbol, data)
                    # Extract score from result dictionary
                    if isinstance(microstructure_result, dict) and 'score' in microstructure_result:
                        microstructure_score = microstructure_result['score']
                    else:
                        microstructure_score = microstructure_result  # Assume it's a float
                    score += microstructure_score * 0.2
                except Exception as e:
                    self.logger.warning(f"Error in microstructure analysis for {symbol}: {str(e)}")

            # 3. Options Analysis (15% weight)
            if self.options_analyzer is not None:
                try:
                    options_result = self.options_analyzer.analyze(symbol, data)
                    # Extract score from result dictionary
                    if isinstance(options_result, dict) and 'score' in options_result:
                        options_score = options_result['score']
                    else:
                        options_score = options_result  # Assume it's a float
                    score += options_score * 0.15
                except Exception as e:
                    self.logger.warning(f"Error in options analysis for {symbol}: {str(e)}")

            # 4. Sentiment Analysis (15% weight)
            sentiment_score = 0.5  # Default neutral score
            if self.sentiment_analyzer is not None:
                try:
                    sentiment_result = self.sentiment_analyzer.analyze(symbol)
                    # Extract score from result dictionary
                    if isinstance(sentiment_result, dict) and 'score' in sentiment_result:
                        sentiment_score = sentiment_result['score']
                    else:
                        sentiment_score = sentiment_result  # Assume it's a float
                    score += sentiment_score * 0.15
                except Exception as e:
                    self.logger.warning(f"Error in sentiment analysis for {symbol}: {str(e)}")
                    sentiment_score = 0.5  # Fallback to neutral

            # 5. Reinforcement Learning (10% weight)
            rl_score = 0.5  # Default neutral score
            if self.rl_trader is not None:
                try:
                    # Use get_action method instead of predict
                    state = self._get_state_for_rl(data)
                    rl_action = self.rl_trader.get_action(state)
                    # Convert action to score (0=Sell->0.0, 1=Hold->0.5, 2=Buy->1.0)
                    rl_score = float(rl_action) / 2.0 if rl_action is not None else 0.5
                    score += rl_score * 0.1
                    self.logger.info(f"RL prediction score for {symbol}: {rl_score:.2f}")
                except Exception as e:
                    self.logger.warning(f"Error in RL prediction for {symbol}: {str(e)}")
                    score += 0.5 * 0.1  # Add neutral score as fallback

            # 6. Deep Learning Prediction (10% weight)
            dl_score = 0.5  # Default neutral score
            if HAS_ADVANCED_MODULES and self.deep_learning_predictor is not None:
                try:
                    dl_score = self.deep_learning_predictor.get_prediction_score(data, symbol)
                    score += dl_score * 0.1
                    self.logger.info(f"Deep Learning prediction score for {symbol}: {dl_score:.2f}")
                except Exception as e:
                    self.logger.warning(f"Error in deep learning prediction for {symbol}: {str(e)}")
                    dl_score = 0.5  # Fallback to neutral score

            # 7. Advanced Prediction (20% weight)
            adv_score = 0.5  # Default neutral score
            confidence_interval = None
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_predictor') and self.advanced_predictor is not None:
                try:
                    # Get prediction score from advanced model
                    adv_score = self.advanced_predictor.get_prediction_score(data, symbol)
                    score += adv_score * 0.2

                    # Get confidence interval for logging
                    confidence_interval = self.advanced_predictor.get_confidence_interval(data, symbol)
                    if confidence_interval:
                        self.logger.info(f"Advanced prediction for {symbol}: {confidence_interval['prediction']:.2f} "
                                       f"[{confidence_interval['lower_bound']:.2f} - {confidence_interval['upper_bound']:.2f}] "
                                       f"Score: {adv_score:.2f}")
                    else:
                        self.logger.info(f"Advanced prediction score for {symbol}: {adv_score:.2f}")
                except Exception as e:
                    self.logger.warning(f"Error in advanced prediction for {symbol}: {str(e)}")

            # 8. Dual Prediction Confirmation
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_trading') and self.advanced_trading is not None:
                try:
                    # Use the sentiment_score and rl_score from above, with fallbacks
                    if 'sentiment_score' not in locals():
                        sentiment_score = 0.5  # Neutral score as fallback
                    if 'rl_score' not in locals():
                        rl_score = 0.5  # Neutral score as fallback

                    # Get options score (fallback to neutral if not available)
                    options_score = 0.5  # Neutral score as fallback

                    # Get technical score (from steps 1-4)
                    technical_score = (tech_score * 0.4 + sentiment_score * 0.3 + options_score * 0.2 + rl_score * 0.1) / 1.0

                    # Get ML score (from steps 6-7)
                    ml_score = (dl_score * 0.33 + adv_score * 0.67) if dl_score > 0 else adv_score

                    # Get dual prediction confirmation
                    dual_prediction = self.advanced_trading.get_dual_prediction_confirmation(
                        symbol, data, technical_score, ml_score)

                    # Adjust score based on confirmation
                    if dual_prediction['agree']:
                        # Boost score if predictions agree
                        score = score * 1.1
                        score = min(1.0, score)  # Cap at 1.0
                    else:
                        # Reduce score if predictions disagree
                        score = score * 0.9
                except Exception as e:
                    self.logger.warning(f"Error in dual prediction for {symbol}: {str(e)}")

            # 9. Historical Pattern Matching
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_trading') and self.advanced_trading is not None:
                try:
                    # Find similar historical patterns
                    pattern_results = self.advanced_trading.find_similar_historical_patterns(
                        symbol, data, download_func=self.download_data)

                    # Adjust score based on pattern matching
                    if pattern_results and 'summary' in pattern_results and pattern_results['summary']:
                        pattern_score = pattern_results['summary'].get('pattern_score', 0)

                        # Convert to 0-1 scale
                        normalized_pattern_score = (pattern_score + 0.05) / 0.1  # -5% to +5% -> 0 to 1
                        normalized_pattern_score = max(0, min(1, normalized_pattern_score))

                        # Add to score with 10% weight
                        score = score * 0.9 + normalized_pattern_score * 0.1

                        self.logger.info(f"Pattern matching for {symbol}: Score={normalized_pattern_score:.2f}, "
                                       f"Expected return={pattern_score:.2%}")
                except Exception as e:
                    self.logger.warning(f"Error in pattern matching for {symbol}: {str(e)}")

            # 10. Multi-Timeframe Analysis
            if HAS_ADVANCED_MODULES and hasattr(self, 'multi_timeframe') and self.multi_timeframe is not None:
                try:
                    # Analyze multiple timeframes
                    multi_tf_result = self.multi_timeframe.analyze(symbol, self.download_data, self.analyze_stock)

                    if multi_tf_result and 'score' in multi_tf_result:
                        multi_tf_score = multi_tf_result['score']

                        # Add alignment bonus if signals align across timeframes
                        alignment_bonus = 0.05 if multi_tf_result.get('aligned', False) else 0

                        # Add to score with 15% weight
                        score = score * 0.85 + multi_tf_score * 0.15 + alignment_bonus

                        self.logger.info(f"Multi-timeframe analysis for {symbol}: Score={multi_tf_score:.2f}, "
                                       f"Aligned={multi_tf_result.get('aligned', False)}, "
                                       f"Confidence={multi_tf_result.get('confidence', 0):.2f}")
                except Exception as e:
                    self.logger.warning(f"Error in multi-timeframe analysis for {symbol}: {str(e)}")

            # 11. Alternative Data Integration
            if HAS_ADVANCED_MODULES and hasattr(self, 'alternative_data') and self.alternative_data is not None:
                try:
                    # Get alternative data score
                    alt_data_score = self.alternative_data.integrate_alternative_data(symbol)

                    # Add to score with 10% weight
                    score = score * 0.9 + alt_data_score * 0.1

                    self.logger.info(f"Alternative data for {symbol}: Score={alt_data_score:.2f}")
                except Exception as e:
                    self.logger.warning(f"Error in alternative data integration for {symbol}: {str(e)}")

            # 12. Dual Network Prediction
            if HAS_ADVANCED_MODULES and hasattr(self, 'dual_network') and self.dual_network is not None:
                try:
                    # Get dual network prediction
                    dual_result = self.dual_network.predict(symbol, data)

                    if dual_result and 'prediction_score' in dual_result:
                        dual_score = dual_result['prediction_score']
                        dual_confidence = dual_result.get('confidence', 0.5)

                        # Add to score with weight based on confidence
                        weight = 0.15 * dual_confidence
                        score = score * (1 - weight) + dual_score * weight

                        self.logger.info(f"Dual network prediction for {symbol}: Score={dual_score:.2f}, "
                                       f"Confidence={dual_confidence:.2f}")
                except Exception as e:
                    self.logger.warning(f"Error in dual network prediction for {symbol}: {str(e)}")

            # 13. Adaptive RL Prediction
            if HAS_ADVANCED_MODULES and hasattr(self, 'adaptive_rl') and self.adaptive_rl is not None:
                try:
                    # Get RL prediction
                    rl_score = self.adaptive_rl.predict(symbol, data)

                    # Add to score with 10% weight
                    score = score * 0.9 + rl_score * 0.1

                    self.logger.info(f"Adaptive RL prediction for {symbol}: Score={rl_score:.2f}")
                except Exception as e:
                    self.logger.warning(f"Error in adaptive RL prediction for {symbol}: {str(e)}")

            return min(max(score, 0.0), 1.0)

        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {str(e)}")
            return 0.0

    def _get_state_for_rl(self, data):
        """Get state representation for RL agent"""
        try:
            if data is None or data.empty:
                return np.array([0.5] * 10, dtype=np.float32)  # Default neutral state

            # Get latest values
            latest = data.iloc[-1]

            # Create state vector with normalized values
            state = [
                (latest['Close'] - data['Close'].min()) / (data['Close'].max() - data['Close'].min() + 1e-8),
                (latest['Volume'] - data['Volume'].min()) / (data['Volume'].max() - data['Volume'].min() + 1e-8),
                latest.get('RSI', 50) / 100.0,  # Normalize RSI to 0-1
                (latest.get('MACD', 0) + 1) / 2,  # Normalize MACD roughly to 0-1
                latest.get('BB_position', 0.5),  # Bollinger Band position
                latest.get('SMA_20_ratio', 1.0),  # Price to SMA ratio
                latest.get('EMA_12_ratio', 1.0),  # Price to EMA ratio
                latest.get('Stoch_K', 50) / 100.0,  # Stochastic K
                latest.get('Williams_R', -50) / -100.0,  # Williams %R
                latest.get('CCI', 0) / 200.0 + 0.5  # Commodity Channel Index
            ]

            # Ensure all values are between 0 and 1
            state = [max(0, min(1, val)) for val in state]

            return np.array(state, dtype=np.float32)
        except Exception as e:
            self.logger.warning(f"Error creating RL state: {e}")
            return np.array([0.5] * 10, dtype=np.float32)  # Default neutral state

    def should_buy(self, symbol: str, data: pd.DataFrame) -> bool:
        """Enhanced buy decision logic with memory-augmented learning"""
        try:
            if symbol in self.positions:
                return False

            if len(self.positions) >= self.max_positions:
                return False

            latest = data.iloc[-1]

            # Get values, handling Series objects
            close_price = latest['Close']
            volume = latest['Volume']

            # Convert pandas Series to scalar if needed
            if hasattr(close_price, 'iloc') and len(close_price) > 0:
                close_price = close_price.iloc[-1]
            if hasattr(volume, 'iloc') and len(volume) > 0:
                volume = volume.iloc[-1]

            # Basic checks
            if close_price < self.min_price:
                return False

            if volume < self.min_volume:
                return False

            # Calculate score
            score = self.analyze_stock(symbol, data)

            # Variables to store additional memory-augmented insights
            memory_insights = {}
            mistake_probabilities = {}

            # Use memory-augmented learning to enhance decision
            if HAS_ADVANCED_MODULES and hasattr(self, 'memory_learning') and self.memory_learning:
                try:
                    # Extract current market context
                    market_context = self._extract_market_context(symbol, data)

                    # Retrieve relevant past experiences
                    relevant_experiences = self.memory_learning.retrieve_relevant_experiences(market_context, top_k=5)

                    if relevant_experiences:
                        self.logger.info(f"Retrieved {len(relevant_experiences)} relevant experiences for {symbol}")

                        # Analyze past performance in similar conditions
                        win_count = sum(1 for exp in relevant_experiences if exp.get('result', 0) > 0)
                        loss_count = sum(1 for exp in relevant_experiences if exp.get('result', 0) < 0)
                        win_rate = win_count / len(relevant_experiences) if relevant_experiences else 0

                        # Log historical performance in similar conditions
                        self.logger.info(f"Historical performance in similar conditions for {symbol}: "
                                        f"Win rate: {win_rate:.2f} ({win_count}/{len(relevant_experiences)})")

                        # Store insights for later use
                        memory_insights = {
                            'win_rate': win_rate,
                            'win_count': win_count,
                            'loss_count': loss_count,
                            'total_experiences': len(relevant_experiences)
                        }

                        # Adjust score based on historical performance
                        memory_weight = 0.2  # 20% weight for memory-based insights
                        memory_score = win_rate

                        # Apply win rate adjustment to score
                        adjusted_score = score * (1 - memory_weight) + memory_score * memory_weight
                        self.logger.info(f"Memory-adjusted score for {symbol}: {score:.3f} → {adjusted_score:.3f}")
                        score = adjusted_score

                    # Predict mistake probabilities to avoid common errors
                    mistake_probabilities = self.memory_learning.predict_mistake_probability(market_context)

                    # If high probability of direction error, be more cautious
                    if mistake_probabilities.get('direction_error', 0) > 0.3:
                        score *= 0.9  # Reduce score if high direction error probability
                        self.logger.info(f"Reducing score due to high direction error probability ({mistake_probabilities.get('direction_error', 0):.2f})")

                    # If high overall mistake probability, increase threshold
                    if mistake_probabilities.get('overall_mistake', 0) > 0.4:
                        self.logger.info(f"Increasing buy threshold due to high overall mistake probability ({mistake_probabilities.get('overall_mistake', 0):.2f})")

                except Exception as e:
                    self.logger.warning(f"Error using memory-augmented learning for {symbol}: {str(e)}")

            # Calculate score
            score = self.analyze_stock(symbol, data)

            # Detect market regime and adjust parameters
            market_regime_info = None
            buy_threshold = 0.7  # Default threshold

            # Adjust buy threshold based on mistake probabilities
            if 'overall_mistake' in mistake_probabilities and mistake_probabilities['overall_mistake'] > 0.4:
                buy_threshold += 0.1  # Increase threshold if high mistake probability

            # Use Quantum AI Smart Agent if available
            quantum_decision = None
            if self.use_quantum_agent and self.quantum_agent:
                try:
                    # Update quantum agent beliefs with market data
                    self.quantum_agent.update_beliefs({symbol: data})

                    # Get quantum decision
                    quantum_decision = self.quantum_agent.decide_action(symbol, data)

                    # Store quantum signals for this symbol
                    if 'quantum_signals' in quantum_decision:
                        self.quantum_signals[symbol] = quantum_decision['quantum_signals']

                    # Log quantum decision
                    self.logger.info(f"Quantum AI decision for {symbol}: {quantum_decision['action']} " +
                                  f"with {quantum_decision['confidence']:.2f} confidence in {quantum_decision['market_regime']} regime")

                    # Influence score based on quantum decision with 30% weight
                    quantum_weight = 0.3
                    quantum_score = 0.5  # Neutral default

                    # Convert quantum action to score
                    if quantum_decision['action'] == 'buy':
                        quantum_score = 0.5 + (quantum_decision['confidence'] * 0.5)  # 0.5-1.0 range
                    elif quantum_decision['action'] == 'sell':
                        quantum_score = 0.5 - (quantum_decision['confidence'] * 0.5)  # 0.0-0.5 range

                    # Blend quantum score with existing score
                    adjusted_score = score * (1 - quantum_weight) + quantum_score * quantum_weight
                    self.logger.info(f"Quantum-adjusted score for {symbol}: {score:.3f} → {adjusted_score:.3f}")
                    score = adjusted_score

                except Exception as e:
                    self.logger.warning(f"Error using Quantum AI agent for {symbol}: {str(e)}")

            # First try adaptive market hypothesis
            if HAS_ADVANCED_MODULES and hasattr(self, 'adaptive_market') and self.adaptive_market is not None:
                try:
                    # Get market efficiency information
                    efficiency_info = self.adaptive_market.detect_market_efficiency(symbol, data)

                    # Log market efficiency
                    self.logger.info(f"Detected market efficiency for {symbol}: {efficiency_info['efficiency']:.2f} "
                                   f"({efficiency_info['regime']} regime)")

                    # Get trading parameters for this efficiency level
                    efficiency_params = self.adaptive_market.get_regime_trading_params(efficiency_info)

                    # Update buy threshold based on market efficiency
                    buy_threshold = efficiency_params['buy_threshold']

                    # Adjust position size factor based on market efficiency
                    self.position_size_factor = efficiency_params['position_size_factor']

                    # Adjust risk factor based on market efficiency
                    self.risk_per_trade = self.base_risk_per_trade * efficiency_params['risk_factor']

                    # Adjust stop loss and take profit
                    self.stop_loss = efficiency_params['stop_loss']
                    self.take_profit = efficiency_params['take_profit']
                    self.trailing_stop = efficiency_params['trailing_stop']

                except Exception as e:
                    self.logger.warning(f"Error detecting market efficiency for {symbol}: {str(e)}")

                    # Fall back to market regime detector
                    if HAS_ADVANCED_MODULES and hasattr(self, 'market_regime_detector') and self.market_regime_detector is not None:
                        try:
                            # Get market regime information
                            market_regime_info = self.market_regime_detector.detect_regime(data)

                            # Log market regime
                            self.logger.info(f"Detected market regime: {market_regime_info['label']} with confidence {market_regime_info['confidence']:.2f}")

                            # Get trading parameters for this regime
                            regime_params = self.market_regime_detector.get_regime_trading_params(market_regime_info)

                            # Update buy threshold based on market regime
                            buy_threshold = regime_params['buy_threshold']

                            # Adjust position size factor based on market regime
                            self.position_size_factor = regime_params['position_size_factor']

                            # Adjust risk factor based on market regime
                            self.risk_per_trade = self.base_risk_per_trade * regime_params['risk_factor']

                        except Exception as e:
                            self.logger.warning(f"Error detecting market regime: {str(e)}")
            # Fall back to market regime detector if adaptive market not available
            elif HAS_ADVANCED_MODULES and hasattr(self, 'market_regime_detector') and self.market_regime_detector is not None:
                try:
                    # Get market regime information
                    market_regime_info = self.market_regime_detector.detect_regime(data)

                    # Log market regime
                    self.logger.info(f"Detected market regime: {market_regime_info['label']} with confidence {market_regime_info['confidence']:.2f}")

                    # Get trading parameters for this regime
                    regime_params = self.market_regime_detector.get_regime_trading_params(market_regime_info)

                    # Update buy threshold based on market regime
                    buy_threshold = regime_params['buy_threshold']

                    # Adjust position size factor based on market regime
                    self.position_size_factor = regime_params['position_size_factor']

                    # Adjust risk factor based on market regime
                    self.risk_per_trade = self.base_risk_per_trade * regime_params['risk_factor']

                except Exception as e:
                    self.logger.warning(f"Error detecting market regime: {str(e)}")

            # Minimum score threshold (adjusted for market regime)
            # Convert score to float if it's a numpy array or pandas Series
            if hasattr(score, 'item'):
                score = score.item()
            elif hasattr(score, 'iloc'):
                score = float(score.iloc[0])

            # Convert buy_threshold to float if needed
            if hasattr(buy_threshold, 'item'):
                buy_threshold = buy_threshold.item()
            elif hasattr(buy_threshold, 'iloc'):
                buy_threshold = float(buy_threshold.iloc[0])

            if score < buy_threshold:
                self.logger.info(f"Score {float(score):.2f} below threshold {float(buy_threshold):.2f} for {symbol}")
                return False

            # Check market conditions
            market_score = self.microstructure_analyzer.get_market_condition()
            if market_score < 0.5:  # Avoid buying in weak market conditions
                return False

            # Check sentiment
            sentiment = self.sentiment_analyzer.get_sentiment(symbol)
            if sentiment < 0.3:  # Avoid buying with negative sentiment
                return False

            # Check options flow
            options_flow = self.options_analyzer.get_options_flow(symbol)
            if options_flow < 0.2:  # Avoid buying with weak options flow
                return False

            # Check news sentiment
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_trading') and self.advanced_trading is not None:
                try:
                    news_sentiment = self.advanced_trading.get_news_sentiment(symbol)
                    if news_sentiment < 0.3:  # Very negative news
                        self.logger.info(f"Avoiding {symbol} due to negative news sentiment: {news_sentiment:.2f}")
                        return False
                except Exception as e:
                    self.logger.warning(f"Error checking news sentiment for {symbol}: {str(e)}")

            # Check for high-frequency trading opportunities
            if HAS_ADVANCED_MODULES and self.high_frequency_trader is not None:
                try:
                    # Get order book data (in a real implementation, this would come from the exchange)
                    order_book = self._get_simulated_order_book(symbol, data)

                    # Analyze for HFT opportunities
                    opportunity = self.high_frequency_trader.analyze_market_microstructure(order_book)

                    if opportunity and opportunity['score'] > 0.7:  # High confidence opportunity
                        self.logger.info(f"HFT opportunity detected for {symbol}: {opportunity['opportunity_type']} with score {opportunity['score']:.2f}")
                        return True  # Buy based on HFT signal alone
                except Exception as e:
                    self.logger.warning(f"Error checking HFT opportunities for {symbol}: {str(e)}")

            return True

        except Exception as e:
            self.logger.error(f"Error in buy decision for {symbol}: {str(e)}")
            return False

    def get_watchlist_data(self):
        """Get detailed watchlist data

        Returns:
            dict: Dictionary with watchlist data, keyed by symbol
        """
        try:
            watchlist_dict = {}
            symbols = self.get_watchlist()

            for symbol in symbols:
                try:
                    # Get data for symbol
                    data = self.get_data(symbol, period='1d')

                    if data is not None and not data.empty:
                        # Get current price and previous price
                        price = data['Close'].iloc[-1]
                        prev_price = data['Close'].iloc[-2] if len(data) > 1 else price

                        # Calculate change percentage
                        change = (price / prev_price - 1) * 100

                        # Get volume
                        volume = data['Volume'].iloc[-1]

                        # Get signal
                        signal = "Unknown"
                        if hasattr(self, 'analyze_stock'):
                            score = self.analyze_stock(symbol, data)
                            if score > 0.7:
                                signal = "Buy"
                            elif score < 0.3:
                                signal = "Sell"
                            else:
                                signal = "Hold"

                        # Add to watchlist dictionary
                        watchlist_dict[symbol] = {
                            'symbol': symbol,
                            'price': price,
                            'change': change,
                            'volume': volume,
                            'signal': signal
                        }
                    else:
                        # Add with default values if no data
                        watchlist_dict[symbol] = {
                            'symbol': symbol,
                            'price': 0.0,
                            'change': 0.0,
                            'volume': 0,
                            'signal': "No Data"
                        }
                except Exception as e:
                    self.logger.error(f"Error processing {symbol} for watchlist: {str(e)}")
                    # Add with default values
                    watchlist_dict[symbol] = {
                        'symbol': symbol,
                        'price': 0.0,
                        'change': 0.0,
                        'volume': 0,
                        'signal': "Error"
                    }

            return watchlist_dict
        except Exception as e:
            self.logger.error(f"Error getting watchlist data: {str(e)}")
            return {}

    def get_data(self, symbol, period='6mo', interval='1d'):
        """Get historical data for a symbol

        Args:
            symbol: Trading symbol
            period: Time period (e.g., '1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
            interval: Data interval ('1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo')

        Returns:
            pd.DataFrame: DataFrame with OHLCV data
        """
        try:
            # Check if we have a data provider
            if hasattr(self, 'data_provider') and self.data_provider is not None:
                # Convert period to limit for data provider
                limit = 100  # Default limit

                # Map period to approximate number of data points
                if period == '1d':
                    limit = 1
                elif period == '5d':
                    limit = 5
                elif period == '1mo':
                    limit = 30
                elif period == '3mo':
                    limit = 90
                elif period == '6mo':
                    limit = 180
                elif period == '1y':
                    limit = 365
                elif period == '2y':
                    limit = 730
                elif period == '5y':
                    limit = 1825
                elif period == '10y':
                    limit = 3650
                elif period == 'ytd':
                    # Calculate days from beginning of year
                    today = datetime.now()
                    start_of_year = datetime(today.year, 1, 1)
                    limit = (today - start_of_year).days
                elif period == 'max':
                    limit = 10000  # Very large number

                # Get data from provider
                data = self.data_provider.get_historical_data(symbol, interval, limit)

                # Add technical indicators if data is not empty
                if data is not None and not data.empty:
                    # Add indicators
                    if hasattr(self, 'add_indicators'):
                        data = self.add_indicators(data)

                    # Add safe indicators if available
                    if 'HAS_SAFE_INDICATORS' in globals() and HAS_SAFE_INDICATORS:
                        data = add_safe_indicators(data)

                return data
            else:
                # Fall back to yfinance if no data provider
                return self.download_data(symbol, period, interval)
        except Exception as e:
            self.logger.error(f"Error getting data for {symbol}: {str(e)}")
            return pd.DataFrame()

    def load_state(self, filepath=None):
        """Load bot state from file

        Args:
            filepath: Path to state file (if None, uses default path)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Use default filepath if none provided
            if filepath is None:
                filepath = "data/bot_state.json"

            # Check if file exists
            if not os.path.exists(filepath):
                self.logger.warning(f"State file {filepath} does not exist")
                return False

            # Load state from file
            with open(filepath, 'r') as f:
                state = json.load(f)

            # Restore state
            self.balance = state.get('balance', self.initial_balance)
            self.positions = state.get('positions', {})
            self.watched_symbols = state.get('watched_symbols', self.watched_symbols)
            self.last_prices = state.get('last_prices', {})
            self.trades_history = state.get('trades_history', [])

            # Restore advanced parameters
            self.stop_loss = state.get('stop_loss', self.stop_loss)
            self.take_profit = state.get('take_profit', self.take_profit)
            self.trailing_stop = state.get('trailing_stop', self.trailing_stop)
            self.position_size_factor = state.get('position_size_factor', self.position_size_factor)
            self.risk_per_trade = state.get('risk_per_trade', self.risk_per_trade)

            self.logger.info(f"Loaded state from {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Error loading state: {str(e)}")
            return False

    def record_trade_result(self, trade_result: Dict[str, Any]) -> None:
        """
        Record trading result and update learning systems

        Args:
            trade_result: Dictionary containing trade details including:
                - symbol: Trading symbol
                - entry_price: Entry price
                - exit_price: Exit price
                - shares: Number of shares
                - entry_time: Entry timestamp
                - exit_time: Exit timestamp
                - profit_loss: Profit/loss amount
                - profit_loss_pct: Profit/loss percentage
                - trade_type: Buy or Sell
                - market_regime: Market regime during trade
                - indicators: Technical indicators at entry
        """
        self.logger.info(f"Recording trade result for {trade_result.get('symbol')}: "
                         f"P/L: {trade_result.get('profit_loss_pct', 0):.2f}%")

        # Add trade to history
        if 'trade_id' not in trade_result:
            trade_result['trade_id'] = len(self.trades_history) + 1

        self.trades_history.append(trade_result)

        # Get market regime if not provided
        if 'market_regime' not in trade_result and HAS_ADVANCED_MODULES and hasattr(self, 'market_regime_detector'):
            try:
                symbol = trade_result.get('symbol')
                data = self.get_data(symbol)
                regime_info = self.market_regime_detector.detect_regime(data)
                trade_result['market_regime'] = regime_info.get('label', 'unknown')
                trade_result['regime_confidence'] = regime_info.get('confidence', 0)
            except Exception as e:
                self.logger.warning(f"Error detecting market regime for trade: {str(e)}")

        # Identify mistake type if applicable
        if trade_result.get('profit_loss', 0) < 0 and 'mistake_type' not in trade_result:
            trade_result['mistake_type'] = self._identify_mistake_type(trade_result)

        # Calculate max profit potential (for timing error analysis)
        if 'max_profit_potential' not in trade_result:
            try:
                symbol = trade_result.get('symbol')
                entry_time = trade_result.get('entry_time')
                exit_time = trade_result.get('exit_time')
                entry_price = trade_result.get('entry_price')

                if symbol and entry_time and exit_time and entry_price:
                    # Get price data between entry and exit
                    price_data = self.get_data(symbol, entry_time, exit_time)
                    if not price_data.empty:
                        max_price = price_data['High'].max()
                        min_price = price_data['Low'].min()

                        # Calculate best possible outcome
                        if trade_result.get('trade_type') == 'buy':
                            max_potential = (max_price - entry_price) / entry_price
                        else:  # sell/short
                            max_potential = (entry_price - min_price) / entry_price

                        trade_result['max_profit_potential'] = max_potential
            except Exception as e:
                self.logger.warning(f"Error calculating max profit potential: {str(e)}")

        # Update continuous learning engine
        if HAS_ADVANCED_MODULES and hasattr(self, 'continuous_learning_engine') and self.continuous_learning_engine:
            try:
                learning_updates = self.continuous_learning_engine.record_trade_result(trade_result)
                self.logger.info(f"Updated continuous learning engine: {learning_updates}")

                # If this was a losing trade, analyze mistake patterns
                if trade_result.get('profit_loss', 0) < 0:
                    symbol = trade_result.get('symbol')
                    mistake_analysis = self.continuous_learning_engine.analyze_trade_mistakes(symbol, 90)  # Last 90 days
                    self.logger.info(f"Mistake analysis for {symbol}: {mistake_analysis}")

                    # Get learning recommendations for future trades
                    market_regime = trade_result.get('market_regime', 'unknown')
                    recommendations = self.continuous_learning_engine.generate_learning_recommendations(symbol, market_regime)
                    self.logger.info(f"Learning recommendations for {symbol}: {recommendations}")

                    # Apply strategy adjustments if they exist
                    if 'strategy_adjustments' in recommendations:
                        self._apply_strategy_adjustments(symbol, recommendations['strategy_adjustments'])
            except Exception as e:
                self.logger.warning(f"Error updating continuous learning: {str(e)}")

    def _identify_mistake_type(self, trade_result: Dict[str, Any]) -> str:
        """Identify the type of mistake for a losing trade"""
        symbol = trade_result.get('symbol')
        entry_price = trade_result.get('entry_price')
        exit_price = trade_result.get('exit_price')
        entry_time = trade_result.get('entry_time')
        exit_time = trade_result.get('exit_time')
        stop_loss = trade_result.get('stop_loss')
        take_profit = trade_result.get('take_profit')

        # Default mistake type
        mistake_type = 'unknown'

        try:
            # Get price data and indicators for analysis
            price_data = self.get_data(symbol, entry_time, exit_time)

            if price_data.empty:
                return mistake_type

            # Check for timing error
            if entry_time and exit_time:
                # Check if price moved in the right direction after exit
                post_exit_data = self.get_data(symbol, exit_time,
                                              pd.to_datetime(exit_time) + timedelta(days=3))

                if not post_exit_data.empty:
                    if trade_result.get('trade_type') == 'buy':
                        # For buy trades, check if price increased after exit
                        if post_exit_data['Close'].iloc[-1] > exit_price * 1.02:  # 2% higher
                            mistake_type = 'timing_error'  # Sold too early
                    else:
                        # For sell/short trades, check if price decreased after exit
                        if post_exit_data['Close'].iloc[-1] < exit_price * 0.98:  # 2% lower
                            mistake_type = 'timing_error'  # Covered too early

            # Check for direction error - entered in the wrong direction
            if trade_result.get('prediction_direction') and trade_result.get('actual_direction'):
                if trade_result.get('prediction_direction') != trade_result.get('actual_direction'):
                    mistake_type = 'direction_error'

            # Check for stop loss error - hit stop loss but would have recovered
            if stop_loss and abs(exit_price - stop_loss) / stop_loss < 0.01:  # Exited near stop loss
                # Check if price recovered after hitting stop loss
                post_exit_data = self.get_data(symbol, exit_time,
                                              pd.to_datetime(exit_time) + timedelta(days=5))

                if not post_exit_data.empty:
                    if trade_result.get('trade_type') == 'buy':
                        # For buy trades, check if price recovered after stop loss
                        if post_exit_data['Close'].max() > entry_price:
                            mistake_type = 'stop_loss_error'  # Stop loss was too tight
                    else:
                        # For sell/short trades
                        if post_exit_data['Close'].min() < entry_price:
                            mistake_type = 'stop_loss_error'  # Stop loss was too tight

            # Check for size error - position size was too large for volatility
            if trade_result.get('position_size_factor', 0) > 0.2 and trade_result.get('volatility', 0) > 0.03:
                mistake_type = 'size_error'  # Position too large for volatility

            # Check for market regime error
            if HAS_ADVANCED_MODULES and hasattr(self, 'market_regime_detector'):
                try:
                    # Compare detected vs actual regime
                    detected_regime = trade_result.get('market_regime', 'unknown')

                    # Determine actual regime from what actually happened
                    price_change = (exit_price - entry_price) / entry_price
                    volatility = price_data['Close'].pct_change().std() * np.sqrt(252)

                    actual_regime = 'unknown'
                    if volatility > 0.03:  # High volatility
                        actual_regime = 'volatile'
                    elif abs(price_change) < 0.01:  # Small price change
                        actual_regime = 'ranging'
                    elif price_change > 0.02:  # Strong uptrend
                        actual_regime = 'trending_up'
                    elif price_change < -0.02:  # Strong downtrend
                        actual_regime = 'trending_down'

                    if detected_regime != 'unknown' and actual_regime != 'unknown' and detected_regime != actual_regime:
                        mistake_type = 'market_regime_error'
                except Exception as e:
                    self.logger.warning(f"Error identifying market regime error: {str(e)}")

            return mistake_type
        except Exception as e:
            self.logger.warning(f"Error identifying mistake type: {str(e)}")
            return mistake_type

    def _apply_strategy_adjustments(self, symbol: str, adjustments: Dict[str, Any]) -> None:
        """Apply strategy adjustments based on learning recommendations"""
        self.logger.info(f"Applying strategy adjustments for {symbol}: {adjustments}")

        # Apply stop loss adjustment
        if 'stop_loss_pct' in adjustments:
            self.stop_loss += adjustments['stop_loss_pct']
            self.stop_loss = max(0.01, min(0.2, self.stop_loss))  # Keep within reasonable bounds
            self.logger.info(f"Adjusted stop loss to {self.stop_loss:.3f}")

        # Apply take profit adjustment
        if 'take_profit_pct' in adjustments:
            self.take_profit += adjustments['take_profit_pct']
            self.take_profit = max(0.02, min(0.5, self.take_profit))  # Keep within reasonable bounds
            self.logger.info(f"Adjusted take profit to {self.take_profit:.3f}")

        # Apply position size adjustment
        if 'position_size_pct' in adjustments:
            self.position_size_factor += adjustments['position_size_pct']
            self.position_size_factor = max(0.1, min(0.5, self.position_size_factor))  # Keep within reasonable bounds
            self.logger.info(f"Adjusted position size factor to {self.position_size_factor:.3f}")

        # Adjust risk per trade
        if 'risk_factor' in adjustments:
            self.risk_per_trade = self.base_risk_per_trade * (1 + adjustments['risk_factor'])
            self.risk_per_trade = max(0.005, min(0.05, self.risk_per_trade))  # Keep within reasonable bounds
            self.logger.info(f"Adjusted risk per trade to {self.risk_per_trade:.3f}")

    def _extract_market_context(self, symbol: str, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Extract market context for memory-augmented learning

        Args:
            symbol: The stock symbol
            data: DataFrame with market data

        Returns:
            Dictionary with market context
        """
        try:
            if data.empty:
                return {'symbol': symbol}

            # Get latest data
            latest = data.iloc[-1]

            # Extract technical indicators
            indicators = {}

            # Price-based indicators
            if 'Close' in data.columns:
                close_prices = data['Close']
                indicators['close'] = float(latest['Close'])

                # Calculate basic indicators
                if len(data) > 14:  # Need at least 14 bars
                    # RSI
                    delta = close_prices.diff()
                    gain = delta.clip(lower=0)
                    loss = -delta.clip(upper=0)
                    avg_gain = gain.rolling(window=14).mean()
                    avg_loss = loss.rolling(window=14).mean()
                    rs = avg_gain / avg_loss.replace(0, np.nan)
                    rsi = 100 - (100 / (1 + rs))
                    indicators['rsi'] = float(rsi.iloc[-1])

                    # Moving Averages
                    sma_10 = close_prices.rolling(window=10).mean()
                    sma_20 = close_prices.rolling(window=20).mean()
                    sma_50 = close_prices.rolling(window=50).mean() if len(data) > 50 else None
                    indicators['sma_10'] = float(sma_10.iloc[-1])
                    indicators['sma_20'] = float(sma_20.iloc[-1])
                    indicators['sma_50'] = float(sma_50.iloc[-1]) if sma_50 is not None else None

                    # MACD
                    ema_12 = close_prices.ewm(span=12, adjust=False).mean()
                    ema_26 = close_prices.ewm(span=26, adjust=False).mean()
                    macd = ema_12 - ema_26
                    signal = macd.ewm(span=9, adjust=False).mean()
                    indicators['macd'] = float(macd.iloc[-1])
                    indicators['macd_signal'] = float(signal.iloc[-1])
                    indicators['macd_hist'] = float(macd.iloc[-1] - signal.iloc[-1])

                    # Volatility
                    if len(data) > 20:
                        vol_20 = close_prices.pct_change().rolling(window=20).std() * np.sqrt(252)
                        indicators['volatility_20d'] = float(vol_20.iloc[-1])

                    # Price changes
                    if len(data) > 5:
                        price_change_1d = close_prices.pct_change().iloc[-1]
                        price_change_5d = (close_prices.iloc[-1] / close_prices.iloc[-6] - 1) if len(data) > 5 else None
                        indicators['price_change_1d'] = float(price_change_1d)
                        indicators['price_change_5d'] = float(price_change_5d) if price_change_5d is not None else None

            # Volume-based indicators
            if 'Volume' in data.columns:
                volumes = data['Volume']
                indicators['volume'] = float(latest['Volume'])

                if len(data) > 20:
                    # Volume MAs
                    vol_ma_10 = volumes.rolling(window=10).mean()
                    vol_ma_20 = volumes.rolling(window=20).mean()
                    indicators['vol_ma_10'] = float(vol_ma_10.iloc[-1])
                    indicators['vol_ma_20'] = float(vol_ma_20.iloc[-1])

                    # Volume change
                    vol_change = volumes.pct_change().iloc[-1]
                    indicators['volume_change'] = float(vol_change)

                    # Relative volume
                    rel_vol = volumes.iloc[-1] / vol_ma_20.iloc[-1]
                    indicators['relative_volume'] = float(rel_vol)

            # Determine market regime
            market_regime = 'unknown'

            if HAS_ADVANCED_MODULES and hasattr(self, 'market_regime_detector') and self.market_regime_detector is not None:
                try:
                    regime_info = self.market_regime_detector.detect_regime(data)
                    market_regime = regime_info.get('label', 'unknown')
                except Exception as e:
                    self.logger.warning(f"Error detecting market regime: {str(e)}")
            else:
                # Simple regime detection fallback
                if 'rsi' in indicators and 'volatility_20d' in indicators:
                    rsi = indicators['rsi']
                    vol = indicators['volatility_20d']

                    if vol > 0.03:  # High volatility
                        market_regime = 'volatile'
                    elif 30 <= rsi <= 70:  # Normal RSI
                        if 'price_change_5d' in indicators:
                            change_5d = indicators['price_change_5d']
                            if change_5d > 0.02:
                                market_regime = 'trending_up'
                            elif change_5d < -0.02:
                                market_regime = 'trending_down'
                            else:
                                market_regime = 'ranging'
                    elif rsi > 70:  # Overbought
                        market_regime = 'reversal'
                    elif rsi < 30:  # Oversold
                        market_regime = 'reversal'

            # Create context dictionary
            context = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'indicators': indicators,
                'market_regime': market_regime
            }

            return context

        except Exception as e:
            self.logger.warning(f"Error extracting market context: {str(e)}")
            return {'symbol': symbol}

    def calculate_position_size(self, balance: float, risk_per_trade: float, symbol: str = None, data: pd.DataFrame = None) -> float:
        """Enhanced position sizing with risk management and adaptive sizing"""
        try:
            # Use the risk_per_trade parameter or the class attribute
            actual_risk = risk_per_trade if risk_per_trade > 0 else self.risk_per_trade

            # Base position size - adjust based on risk per trade
            base_size = balance * min(self.position_size_factor, actual_risk * 5)

            # Adjust for market volatility
            volatility_factor = 0.8  # Default value
            if self.microstructure_analyzer is not None:
                try:
                    market_volatility = self.microstructure_analyzer.get_market_volatility()
                    volatility_factor = max(0.5, 1 - market_volatility)
                except Exception as e:
                    self.logger.warning(f"Error getting market volatility: {str(e)}")

            # Adjust for portfolio correlation
            correlation_factor = 1.0  # Default value
            try:
                correlation_factor = self.calculate_correlation_factor()
            except Exception as e:
                self.logger.warning(f"Error calculating correlation factor: {str(e)}")

            # Calculate standard position size
            standard_position_size = base_size * volatility_factor * correlation_factor * (1 / risk_per_trade)

            # Use adaptive position sizing if available
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_trading') and self.advanced_trading is not None and symbol and data is not None:
                try:
                    # Get prediction score
                    prediction_score = self.analyze_stock(symbol, data)

                    # Convert numpy.float64 to Python float if needed
                    if hasattr(prediction_score, 'item'):
                        prediction_score = float(prediction_score.item())

                    # Get confidence interval if available
                    confidence_interval = None
                    if hasattr(self, 'advanced_predictor') and self.advanced_predictor is not None:
                        try:
                            confidence_interval = self.advanced_predictor.get_confidence_interval(data, symbol)
                        except Exception:
                            pass

                    # Calculate adaptive position size
                    adaptive_size = self.advanced_trading.calculate_adaptive_position_size(
                        symbol, data, balance, self.position_size_factor, prediction_score, confidence_interval)

                    # Use the smaller of the two sizes for safety
                    position_size = min(standard_position_size, adaptive_size)
                    self.logger.info(f"Using adaptive position sizing for {symbol}: Standard={standard_position_size:.2f}, "
                                   f"Adaptive={adaptive_size:.2f}, Final={position_size:.2f}")
                except Exception as e:
                    self.logger.warning(f"Error calculating adaptive position size for {symbol}: {str(e)}")
                    position_size = standard_position_size
            else:
                position_size = standard_position_size

            # Ensure minimum and maximum limits
            min_size = balance * 0.1  # Minimum 10% of balance
            max_size = balance * 0.4  # Maximum 40% of balance

            return min(max(position_size, min_size), max_size)

        except Exception as e:
            self.logger.error(f"Error calculating position size: {str(e)}")
            return balance * 0.2  # Default to 20% on error

    def calculate_correlation_factor(self) -> float:
        """Calculate portfolio correlation factor"""
        # Default implementation returns 1.0 (no adjustment)
        return 1.0

    def calculate_stop_loss(self, price: float, stop_loss_pct: float) -> float:
        """Calculate stop loss price"""
        return price * (1 - stop_loss_pct)

    def calculate_take_profit(self, price: float, take_profit_pct: float) -> float:
        """Calculate take profit price"""
        return price * (1 + take_profit_pct)

    def buy_stock(self, symbol: str, shares: int, price: float):
        """Buy a stock"""
        try:
            cost = shares * price
            if cost > self.balance:
                self.logger.warning(f"Insufficient funds to buy {shares} shares of {symbol}")
                return False

            self.balance -= cost

            # Get data for dynamic stop loss calculation
            data = None
            stop_loss_pct = self.stop_loss
            take_profit_pct = self.take_profit
            trailing_stop_pct = self.trailing_stop

            # Use dynamic stops if available
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_trading') and self.advanced_trading is not None:
                try:
                    # Get data for the symbol
                    data = self.get_data(symbol)
                    if data is not None:
                        # Calculate dynamic stops
                        stops = self.advanced_trading.calculate_dynamic_stops(
                            symbol, data, self.stop_loss, self.take_profit)

                        # Update stop levels
                        stop_loss_pct = stops['stop_loss_pct']
                        take_profit_pct = stops['take_profit_pct']
                        trailing_stop_pct = stops['trailing_stop_pct']

                        self.logger.info(f"Using dynamic stops for {symbol}: "
                                       f"Stop loss={stop_loss_pct:.2%}, "
                                       f"Take profit={take_profit_pct:.2%}, "
                                       f"Trailing stop={trailing_stop_pct:.2%}")
                except Exception as e:
                    self.logger.warning(f"Error calculating dynamic stops for {symbol}: {str(e)}")

            # Create position with stop levels
            position = {
                'symbol': symbol,
                'shares': shares,
                'entry_price': price,
                'stop_loss': price * (1 - stop_loss_pct),
                'take_profit': price * (1 + take_profit_pct),
                'trailing_stop': price * (1 - trailing_stop_pct),
                'buy_time': datetime.now(),
                'buy_date': datetime.now().date(),
                'position_size_pct': (cost / self.initial_balance) * 100,  # Record position size as percentage
                'signal_time': datetime.now(),
                'execution_time': datetime.now(),
                'expected_price': price  # For execution quality analysis
            }

            self.positions[symbol] = position
            self.last_prices[symbol] = price
            self.highest_prices[symbol] = price

            # Record trade for history
            trade = {
                'symbol': symbol,
                'type': 'buy',
                'shares': shares,
                'price': price,
                'cost': cost,
                'position_size_pct': position['position_size_pct'],
                'signal_time': position['signal_time'],
                'execution_time': position['execution_time'],
                'expected_price': position['expected_price']
            }

            # Add to trades history
            self.trades_history.append(trade)

            # Track performance with features used
            if HAS_ADVANCED_MODULES and hasattr(self, 'performance_tracker') and self.performance_tracker is not None:
                # Determine which features were used in this trade
                features_used = []

                if hasattr(self, 'multi_timeframe') and self.multi_timeframe is not None:
                    features_used.append('multi_timeframe')

                if hasattr(self, 'alternative_data') and self.alternative_data is not None:
                    features_used.append('alternative_data')

                if hasattr(self, 'quantum_optimizer') and self.quantum_optimizer is not None:
                    features_used.append('quantum_optimization')

                if hasattr(self, 'federated_learner') and self.federated_learner is not None:
                    features_used.append('federated_learning')

                if hasattr(self, 'explainable_ai') and self.explainable_ai is not None:
                    features_used.append('explainable_ai')

                if hasattr(self, 'adaptive_rl') and self.adaptive_rl is not None:
                    features_used.append('adaptive_rl')

                if hasattr(self, 'adaptive_market') and self.adaptive_market is not None:
                    features_used.append('adaptive_market')

                if hasattr(self, 'dual_network') and self.dual_network is not None:
                    features_used.append('dual_network')

                # Record the trade
                self.performance_tracker.record_trade(trade, features_used)

                # Record portfolio value
                self.performance_tracker.record_portfolio_value(datetime.now(), self.balance + self.get_positions_value())

            # Generate explanation for the trade if explainable AI is available
            if HAS_ADVANCED_MODULES and hasattr(self, 'explainable_ai') and self.explainable_ai is not None:
                try:
                    # Get data for explanation
                    data = self.get_data(symbol)
                    if data is not None:
                        # Get prediction score
                        prediction_score = self.analyze_stock(symbol, data)

                        # Get explanation
                        explanation = self.explainable_ai.explain_trading_decision(
                            symbol, data, prediction_score)

                        # Log explanation
                        self.logger.info(f"Trade explanation for {symbol}: {explanation['summary']}")

                        # Store explanation in position
                        position['explanation'] = explanation
                except Exception as e:
                    self.logger.warning(f"Error generating explanation for {symbol}: {str(e)}")

            self.logger.info(f"Bought {shares} shares of {symbol} at ${price:.2f} (Position: ${cost:.2f}, {position['position_size_pct']:.1f}% of portfolio)")
            return True

        except Exception as e:
            self.logger.error(f"Error buying {symbol}: {str(e)}")
            return False

    def sell_stock(self, symbol: str, price: float, reason: str):
        """Sell a stock"""
        try:
            if symbol not in self.positions:
                return False

            position = self.positions[symbol]
            proceeds = position['shares'] * price
            self.balance += proceeds

            profit = proceeds - (position['shares'] * position['entry_price'])
            profit_pct = (price - position['entry_price']) / position['entry_price']

            self.total_trades += 1
            if profit > 0:
                self.winning_trades += 1
            self.total_profit += profit

            # Record trade for history and analysis
            trade = {
                'symbol': symbol,
                'type': 'sell',
                'shares': position['shares'],
                'price': price,
                'entry_price': position['entry_price'],
                'profit': profit,
                'profit_pct': profit_pct,
                'reason': reason,
                'entry_time': position['buy_time'],
                'execution_time': datetime.now(),
                'holding_period_days': (datetime.now() - position['buy_time']).days
            }

            # Add to trades history
            self.trades_history.append(trade)

            # Track performance with features used
            if HAS_ADVANCED_MODULES and hasattr(self, 'performance_tracker') and self.performance_tracker is not None:
                # Determine which features were used in this trade
                features_used = []

                if hasattr(self, 'multi_timeframe') and self.multi_timeframe is not None:
                    features_used.append('multi_timeframe')

                if hasattr(self, 'alternative_data') and self.alternative_data is not None:
                    features_used.append('alternative_data')

                if hasattr(self, 'quantum_optimizer') and self.quantum_optimizer is not None:
                    features_used.append('quantum_optimization')

                if hasattr(self, 'federated_learner') and self.federated_learner is not None:
                    features_used.append('federated_learning')

                if hasattr(self, 'explainable_ai') and self.explainable_ai is not None:
                    features_used.append('explainable_ai')

                if hasattr(self, 'adaptive_rl') and self.adaptive_rl is not None:
                    features_used.append('adaptive_rl')

                if hasattr(self, 'adaptive_market') and self.adaptive_market is not None:
                    features_used.append('adaptive_market')

                if hasattr(self, 'dual_network') and self.dual_network is not None:
                    features_used.append('dual_network')

                # Record the trade
                self.performance_tracker.record_trade(trade, features_used)

                # Record portfolio value
                self.performance_tracker.record_portfolio_value(datetime.now(), self.balance + self.get_positions_value())

            self.logger.info(f"Sold {position['shares']} shares of {symbol} at ${price:.2f} "
                           f"for {profit_pct:.1%} profit (${profit:.2f}) ({reason})")

            # Analyze execution quality
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_trading') and self.advanced_trading is not None:
                try:
                    execution_quality = self.advanced_trading.analyze_execution_quality(self.trades_history)
                    if execution_quality:
                        self.logger.info(f"Execution quality: Win rate={execution_quality['win_rate']:.1%}, "
                                       f"Avg profit=${execution_quality['average_profit']:.2f}, "
                                       f"Avg holding period={execution_quality['average_holding_period']:.1f} days")
                except Exception as e:
                    self.logger.warning(f"Error analyzing execution quality: {str(e)}")

            del self.positions[symbol]
            return True

        except Exception as e:
            self.logger.error(f"Error selling {symbol}: {str(e)}")
            return False

    def get_positions_value(self) -> float:
        """Get the total value of all positions"""
        try:
            total_value = 0.0
            for symbol, position in self.positions.items():
                # Get current price
                current_price = self.last_prices.get(symbol, position['entry_price'])
                # Calculate position value
                position_value = position['shares'] * current_price
                total_value += position_value
            return total_value
        except Exception as e:
            self.logger.error(f"Error calculating positions value: {str(e)}")
            return 0.0

    def should_buy(self, symbol: str, data: pd.DataFrame) -> bool:
        """Determine if we should buy a stock"""
        try:
            # Check if we already have a position in this stock
            if symbol in self.positions:
                return False

            # Check if we have reached the maximum number of positions
            if len(self.positions) >= self.max_positions:
                return False

            # Get the latest price data
            if data is None or len(data) < 20:  # Need at least 20 days of data
                return False

            # Get the latest price
            current_price = data['Close'].iloc[-1]
            if hasattr(current_price, 'iloc'):
                current_price = current_price.iloc[-1]

            # Check minimum price requirement
            if current_price < self.min_price:
                return False

            # Check volume requirement
            if 'Volume' in data.columns:
                avg_volume = data['Volume'].rolling(window=20).mean().iloc[-1]
                if avg_volume < self.min_volume:
                    return False

            # Check volatility requirement
            if 'Close' in data.columns:
                volatility = data['Close'].pct_change().rolling(window=20).std().iloc[-1]
                if volatility > self.max_volatility:
                    return False

            # Check technical indicators
            # 1. Moving Average Crossover
            ma_crossover = False
            if 'SMA_20' in data.columns and 'SMA_50' in data.columns:
                # Check if 20-day MA crossed above 50-day MA recently
                ma_20 = data['SMA_20'].iloc[-5:]
                ma_50 = data['SMA_50'].iloc[-5:]
                if ma_20.iloc[-1] > ma_50.iloc[-1] and ma_20.iloc[-5] < ma_50.iloc[-5]:
                    ma_crossover = True

            # 2. RSI
            rsi_signal = False
            if 'RSI' in data.columns:
                rsi = data['RSI'].iloc[-1]
                # Buy when RSI is between 40 and 60 (not overbought or oversold)
                if 40 <= rsi <= 60:
                    rsi_signal = True

            # 3. MACD
            macd_signal = False
            if 'MACD' in data.columns:
                macd = data['MACD'].iloc[-1]
                # Buy when MACD is positive
                if macd > 0:
                    macd_signal = True

            # 4. Price above moving averages
            price_above_ma = False
            if 'SMA_20' in data.columns and 'SMA_50' in data.columns:
                if current_price > data['SMA_20'].iloc[-1] and current_price > data['SMA_50'].iloc[-1]:
                    price_above_ma = True

            # Use advanced prediction if available
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_predictor') and self.advanced_predictor is not None:
                try:
                    prediction = self.advanced_predictor.predict(data, symbol)
                    if prediction and prediction.get('action') == 'buy' and prediction.get('confidence', 0) > 0.6:
                        self.logger.info(f"Advanced predictor recommends buying {symbol} with confidence {prediction.get('confidence'):.2f}")
                        return True
                except Exception as e:
                    self.logger.warning(f"Error getting prediction for {symbol}: {str(e)}")

            # Use quantum agent if available
            if self.use_quantum_agent and self.quantum_agent is not None:
                try:
                    # Update quantum agent beliefs
                    self.quantum_agent.update_beliefs({symbol: data})

                    # Get quantum action
                    quantum_action = self.quantum_agent.decide_action(symbol, data)

                    # Store quantum signal
                    self.quantum_signals[symbol] = quantum_action

                    # Check if quantum agent recommends buying
                    if quantum_action['action'] == 'buy' and quantum_action['confidence'] > 0.7:
                        self.logger.info(f"Quantum agent recommends buying {symbol} with confidence {quantum_action['confidence']:.2f}")
                        return True
                except Exception as e:
                    self.logger.warning(f"Error getting quantum action for {symbol}: {str(e)}")

            # Combine signals
            # Need at least 2 positive signals to buy
            signals = [ma_crossover, rsi_signal, macd_signal, price_above_ma]
            positive_signals = sum(signals)

            if positive_signals >= 2:
                self.logger.info(f"Buy signal for {symbol}: {positive_signals} positive signals")
                return True

            return False
        except Exception as e:
            self.logger.error(f"Error in should_buy for {symbol}: {str(e)}")
            return False

    def _traditional_stock_buying(self, top_stocks):
        """Traditional approach to buying stocks"""
        try:
            # Try to buy highest scoring stocks that meet criteria
            for symbol, score, data in top_stocks:
                if self.should_buy(symbol, data):
                    current_price = data['Close'].iloc[-1]
                    if hasattr(current_price, 'iloc'):
                        current_price = current_price.iloc[-1]

                    position_size = self.calculate_position_size(self.balance, 0.02, symbol, data)
                    shares = int(position_size / current_price)

                    if shares > 0:
                        self.buy_stock(symbol, shares, current_price)

                        # Break if we've reached max positions
                        if len(self.positions) >= self.max_positions:
                            self.logger.info("Maximum positions reached")
                            break
        except Exception as e:
            self.logger.error(f"Error in traditional stock buying: {str(e)}")

    def check_positions(self):
        """Check and update all positions with aggressive fast trading logic"""
        try:
            for symbol in list(self.positions.keys()):
                position = self.positions[symbol]

                # Get latest price data
                data = self.download_data(symbol, period='5d')
                if data is None or len(data) == 0:
                    self.logger.warning(f"Could not get price data for {symbol}, skipping position check")
                    continue

                current_price = data['Close'].iloc[-1]
                self.last_prices[symbol] = current_price
                entry_price = position['entry_price']
                entry_time = position.get('entry_time', datetime.now())

                # Calculate profit/loss percentage
                profit_pct = (current_price - entry_price) / entry_price

                # Calculate time held (in minutes)
                time_held = (datetime.now() - entry_time).total_seconds() / 60

                # AGGRESSIVE FAST TRADING LOGIC WITH LLM INTEGRATION
                should_sell = False
                sell_reason = ""

                # 1. LLM DECISION INTEGRATION - Get LLM recommendation first
                llm_decision = self._get_llm_sell_decision(symbol, data, position, profit_pct, time_held)
                if llm_decision['should_sell']:
                    should_sell = True
                    sell_reason = f"LLM decision: {llm_decision['reason']} (confidence: {llm_decision['confidence']:.1%})"

                # 2. FAST PROFIT TAKING - Sell if we hit minimum profit threshold
                elif profit_pct >= self.min_profit_threshold:
                    should_sell = True
                    sell_reason = f"Fast profit taking ({profit_pct:.2%})"

                # 3. TIME-BASED SELLING - Sell if held too long regardless of profit
                elif time_held >= self.max_hold_time_minutes:
                    should_sell = True
                    sell_reason = f"Max hold time reached ({time_held:.1f} min)"

                # 4. SCALPING MODE - Sell on any positive movement if in scalping mode
                elif self.scalping_mode and profit_pct > 0.001:  # 0.1% profit
                    should_sell = True
                    sell_reason = f"Scalping profit ({profit_pct:.2%})"

                # 5. TRADITIONAL STOP LOSS (but more aggressive)
                elif current_price <= position['stop_loss']:
                    should_sell = True
                    sell_reason = 'Stop loss'

                # 6. TRADITIONAL TAKE PROFIT
                elif current_price >= position['take_profit']:
                    should_sell = True
                    sell_reason = 'Take profit'

                # 7. TRAILING STOP
                elif current_price <= position['trailing_stop']:
                    should_sell = True
                    sell_reason = 'Trailing stop'

                # Execute sell if any condition is met
                if should_sell:
                    self.sell_stock(symbol, current_price, sell_reason)
                    continue

                # Update highest price for trailing stop
                if current_price > self.highest_prices.get(symbol, 0):
                    self.highest_prices[symbol] = current_price
                    position['trailing_stop'] = current_price * (1 - self.trailing_stop)
                    self.logger.info(f"Updated trailing stop for {symbol} to ${position['trailing_stop']:.2f}")

                # Get state for RL agent
                if HAS_ADVANCED_MODULES and hasattr(self, 'adaptive_rl') and self.adaptive_rl is not None:
                    try:
                        # Get state
                        state = self.adaptive_rl.get_state(data)

                        # Get action from RL agent
                        action = self.adaptive_rl.get_action(state, symbol)

                        # Execute action
                        if action == 0:  # Sell
                            self.sell_stock(symbol, current_price, "RL agent decision")

                            # Calculate reward (will be used in next update)
                            old_price = position['entry_price']
                            reward = self.adaptive_rl.calculate_reward(action, old_price, current_price, position)

                            # Get next state
                            next_state = state  # In a real implementation, this would be the next state

                            # Update RL agent
                            self.adaptive_rl.update(state, action, reward, next_state)

                            continue  # Skip regular check since we already sold
                        elif action == 2:  # Buy more
                            # In a real implementation, we might buy more shares here
                            pass
                    except Exception as e:
                        self.logger.warning(f"Error using RL agent for {symbol}: {str(e)}")

                # Log current position status
                entry_price = position['entry_price']
                current_pl = (current_price - entry_price) / entry_price
                self.logger.info(f"Position update - {symbol}: Current price=${current_price:.2f}, P/L={current_pl:.1%}")

        except Exception as e:
            self.logger.error(f"Error checking positions: {str(e)}")

    def _get_llm_sell_decision(self, symbol: str, data: pd.DataFrame, position: dict, profit_pct: float, time_held: float) -> dict:
        """Get LLM-powered sell decision for aggressive fast trading

        Args:
            symbol: Stock symbol
            data: Market data
            position: Current position info
            profit_pct: Current profit/loss percentage
            time_held: Time held in minutes

        Returns:
            dict: {'should_sell': bool, 'reason': str, 'confidence': float}
        """
        try:
            # Default decision
            decision = {
                'should_sell': False,
                'reason': 'Hold position',
                'confidence': 0.5
            }

            # Get current market conditions
            current_price = data['Close'].iloc[-1]
            entry_price = position['entry_price']

            # Calculate technical indicators for LLM analysis
            rsi = data.get('RSI', pd.Series([50])).iloc[-1] if 'RSI' in data.columns else 50
            macd = data.get('MACD', pd.Series([0])).iloc[-1] if 'MACD' in data.columns else 0
            volume_ratio = data['Volume'].iloc[-1] / data['Volume'].mean() if len(data) > 1 else 1.0

            # Price momentum (last 5 periods)
            if len(data) >= 5:
                price_momentum = (current_price - data['Close'].iloc[-5]) / data['Close'].iloc[-5]
            else:
                price_momentum = 0

            # LLM DECISION LOGIC FOR FAST TRADING

            # 1. PROFIT MOMENTUM ANALYSIS
            if profit_pct > 0.002:  # 0.2% profit
                if price_momentum < -0.001:  # Price starting to decline
                    decision = {
                        'should_sell': True,
                        'reason': f'Profit secured before reversal (profit: {profit_pct:.2%}, momentum: {price_momentum:.2%})',
                        'confidence': 0.85
                    }
                elif profit_pct > 0.008:  # 0.8% profit - take it!
                    decision = {
                        'should_sell': True,
                        'reason': f'Strong profit target hit ({profit_pct:.2%})',
                        'confidence': 0.95
                    }

            # 2. TECHNICAL INDICATOR ANALYSIS
            elif rsi > 70 and profit_pct > 0:  # Overbought with any profit
                decision = {
                    'should_sell': True,
                    'reason': f'Overbought exit (RSI: {rsi:.1f}, profit: {profit_pct:.2%})',
                    'confidence': 0.75
                }

            # 3. VOLUME SPIKE ANALYSIS
            elif volume_ratio > 2.0 and profit_pct > 0.001:  # High volume with small profit
                decision = {
                    'should_sell': True,
                    'reason': f'Volume spike exit (volume: {volume_ratio:.1f}x, profit: {profit_pct:.2%})',
                    'confidence': 0.70
                }

            # 4. TIME-BASED FAST EXIT
            elif time_held > 15 and profit_pct > 0.001:  # 15 minutes with any profit
                decision = {
                    'should_sell': True,
                    'reason': f'Fast time-based exit ({time_held:.1f}min, profit: {profit_pct:.2%})',
                    'confidence': 0.65
                }

            # 5. LOSS CUTTING
            elif profit_pct < -0.003:  # 0.3% loss - cut it fast
                decision = {
                    'should_sell': True,
                    'reason': f'Fast loss cutting ({profit_pct:.2%})',
                    'confidence': 0.90
                }

            # 6. MACD DIVERGENCE
            elif macd < -0.001 and profit_pct > 0:  # MACD turning negative with profit
                decision = {
                    'should_sell': True,
                    'reason': f'MACD divergence exit (MACD: {macd:.3f}, profit: {profit_pct:.2%})',
                    'confidence': 0.60
                }

            # Log LLM decision
            if decision['should_sell']:
                self.logger.info(f"LLM SELL DECISION for {symbol}: {decision['reason']} (confidence: {decision['confidence']:.1%})")

            return decision

        except Exception as e:
            self.logger.error(f"Error in LLM sell decision for {symbol}: {str(e)}")
            return {
                'should_sell': False,
                'reason': 'Error in analysis',
                'confidence': 0.0
            }

    def scan_stocks(self, symbols: List[str]):
        """Scan stocks for trading opportunities"""
        try:
            self.logger.info(f"Scanning {len(symbols)} stocks for trading opportunities...")
            top_stocks = []

            # Limit symbols based on hardware capability
            if len(symbols) > self.max_symbols_limit:
                self.logger.info(f"Limiting analysis to {self.max_symbols_limit} symbols based on hardware capability")
                symbols = symbols[:self.max_symbols_limit]

            # Define analysis function
            def analyze_symbol(symbol):
                if symbol in self.positions:
                    return None

                data = self.download_data(symbol)
                if data is None:
                    return None

                current_price = data['Close'].iloc[-1]

                # Get stock score
                score = self.analyze_stock(symbol, data)
                return (symbol, score, data, current_price)

            # Use parallel processing if enabled
            if self.use_parallel and self.parallel_jobs > 1:
                self.logger.info(f"Using parallel processing with {self.parallel_jobs} workers for stock scanning")
                with ThreadPoolExecutor(max_workers=self.parallel_jobs) as executor:
                    results = list(executor.map(analyze_symbol, symbols))

                # Process results
                for result in results:
                    if result is not None:
                        symbol, score, data, current_price = result
                        self.last_prices[symbol] = current_price
                        top_stocks.append((symbol, score, data))
            else:
                # Sequential processing
                for symbol in symbols:
                    if symbol in self.positions:
                        continue

                    data = self.download_data(symbol)
                    if data is None:
                        continue

                    current_price = data['Close'].iloc[-1]
                    self.last_prices[symbol] = current_price

                    # Get stock score
                    score = self.analyze_stock(symbol, data)
                    top_stocks.append((symbol, score, data))

            # Sort stocks by score descending
            top_stocks.sort(key=lambda x: x[1], reverse=True)

            # Log top 5 stocks
            self.logger.info("Top scoring stocks:")
            for i, (symbol, score, _) in enumerate(top_stocks[:5]):
                self.logger.info(f"{i+1}. {symbol}: {score:.4f}")

            # Use quantum optimization for portfolio allocation if available
            if HAS_ADVANCED_MODULES and hasattr(self, 'quantum_optimizer') and self.quantum_optimizer is not None:
                try:
                    # Prepare data for optimization
                    symbols = [symbol for symbol, _, _ in top_stocks]
                    returns_data = {}

                    # Get historical returns for each symbol
                    for symbol in symbols:
                        hist_data = self.download_data(symbol, period='3mo')
                        if hist_data is not None and len(hist_data) > 20:
                            returns_data[symbol] = hist_data['Close'].pct_change().dropna()

                    # Create returns DataFrame
                    if returns_data:
                        returns_df = pd.DataFrame(returns_data)

                        # Set optimization constraints
                        constraints = {
                            'max_positions': min(self.max_positions, len(symbols)),
                            'max_weight': 0.3,  # Maximum 30% in any one position
                            'risk_aversion': 1.0  # Balance between return and risk
                        }

                        # Run optimization
                        allocation = self.quantum_optimizer.optimize_portfolio(symbols, returns_df, constraints)

                        self.logger.info(f"Quantum portfolio optimization completed with {len(allocation)} allocations")

                        # Buy stocks based on optimized allocation
                        for symbol, weight in allocation.items():
                            if weight > 0.01:  # Minimum allocation threshold
                                # Get data
                                data = None
                                for s, _, d in top_stocks:
                                    if s == symbol:
                                        data = d
                                        break

                                if data is not None and self.should_buy(symbol, data):
                                    current_price = data['Close'].iloc[-1]
                                    if hasattr(current_price, 'iloc'):
                                        current_price = current_price.iloc[-1]

                                    # Calculate position size based on weight
                                    position_size = self.balance * weight
                                    shares = int(position_size / current_price)

                                    if shares > 0:
                                        self.buy_stock(symbol, shares, current_price)
                                        self.logger.info(f"Bought {symbol} with optimized weight: {weight:.2%}")
                except Exception as e:
                    self.logger.error(f"Error in quantum portfolio optimization: {str(e)}")
                    # Fall back to traditional approach
                    self._traditional_stock_buying(top_stocks)
            else:
                # Traditional approach
                self._traditional_stock_buying(top_stocks)

            # Train advanced prediction models
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_predictor') and self.advanced_predictor is not None:
                try:
                    # Select a few stocks to train models for
                    # Prioritize stocks we own and top scoring stocks
                    train_symbols = list(self.positions.keys())  # Start with our positions

                    # Add top scoring stocks
                    for symbol, score, _ in top_stocks[:3]:  # Top 3 scoring stocks
                        if symbol not in train_symbols:
                            train_symbols.append(symbol)

                    # Add a few major index ETFs for market regime detection
                    for index_symbol in ['SPY', 'QQQ', 'IWM']:
                        if index_symbol in symbols and index_symbol not in train_symbols:
                            train_symbols.append(index_symbol)

                    # Train models for selected symbols
                    for symbol in train_symbols:
                        try:
                            # Get data with longer period for training
                            train_data = self.download_data(symbol, period='1y')
                            if train_data is not None and len(train_data) > 100:  # Need enough data
                                self.logger.info(f"Training advanced prediction model for {symbol}...")
                                self.advanced_predictor.train(train_data, symbol)
                        except Exception as e:
                            self.logger.warning(f"Error training model for {symbol}: {str(e)}")

                    # Train market regime detector using SPY data
                    if hasattr(self, 'market_regime_detector') and self.market_regime_detector is not None:
                        try:
                            if 'SPY' in symbols:
                                spy_data = self.download_data('SPY', period='2y')  # Need longer history
                                if spy_data is not None and len(spy_data) > 252:  # Need at least 1 year
                                    self.logger.info("Training market regime detector...")
                                    self.market_regime_detector.train(spy_data, 'SPY')
                        except Exception as e:
                            self.logger.warning(f"Error training market regime detector: {str(e)}")

                except Exception as e:
                    self.logger.error(f"Error training advanced models: {str(e)}")

            # Train federated learning models
            if HAS_ADVANCED_MODULES and hasattr(self, 'federated_learner') and self.federated_learner is not None:
                try:
                    # Select a few stocks for federated learning
                    federated_symbols = list(self.positions.keys())[:2]  # Start with a couple of our positions

                    # Add a couple of top scoring stocks
                    for symbol, score, _ in top_stocks[:2]:  # Top 2 scoring stocks
                        if symbol not in federated_symbols:
                            federated_symbols.append(symbol)

                    # Train models for selected symbols
                    for symbol in federated_symbols:
                        try:
                            # Get data with longer period for training
                            train_data = self.download_data(symbol, period='1y')
                            if train_data is not None and len(train_data) > 100:  # Need enough data
                                self.logger.info(f"Training federated learning model for {symbol}...")
                                self.federated_learner.train_federated_model(symbol, train_data)
                        except Exception as e:
                            self.logger.warning(f"Error training federated model for {symbol}: {str(e)}")
                except Exception as e:
                    self.logger.error(f"Error training federated models: {str(e)}")

            # Train dual network models
            if HAS_ADVANCED_MODULES and hasattr(self, 'dual_network') and self.dual_network is not None:
                try:
                    # Select a few stocks for dual network training
                    dual_symbols = list(self.positions.keys())[:2]  # Start with a couple of our positions

                    # Add a couple of top scoring stocks
                    for symbol, score, _ in top_stocks[:2]:  # Top 2 scoring stocks
                        if symbol not in dual_symbols:
                            dual_symbols.append(symbol)

                    # Train models for selected symbols
                    for symbol in dual_symbols:
                        try:
                            # Get data with longer period for training
                            train_data = self.download_data(symbol, period='1y')
                            if train_data is not None and len(train_data) > 100:  # Need enough data
                                self.logger.info(f"Training dual network model for {symbol}...")
                                self.dual_network.train(symbol, train_data)
                        except Exception as e:
                            self.logger.warning(f"Error training dual network model for {symbol}: {str(e)}")
                except Exception as e:
                    self.logger.error(f"Error training dual network models: {str(e)}")

            # Analyze portfolio correlation
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_trading') and self.advanced_trading is not None and self.positions:
                try:
                    correlation_analysis = self.advanced_trading.analyze_portfolio_correlation(self.positions, self.download_data)
                    if correlation_analysis:
                        # Log correlation analysis
                        self.logger.info(f"Portfolio correlation analysis: "
                                       f"Avg correlation: {correlation_analysis['average_correlation']:.2f}, "
                                       f"Diversification score: {correlation_analysis['diversification_score']:.2f}")

                        # Log highly correlated pairs
                        if correlation_analysis['high_correlation_pairs']:
                            self.logger.warning(f"Highly correlated pairs detected: "
                                              f"{len(correlation_analysis['high_correlation_pairs'])} pairs")
                            for pair in correlation_analysis['high_correlation_pairs'][:3]:  # Show top 3
                                self.logger.warning(f"Correlated pair: {pair['symbols'][0]} and {pair['symbols'][1]} "
                                                  f"(correlation: {pair['correlation']:.2f})")
                except Exception as e:
                    self.logger.error(f"Error analyzing portfolio correlation: {str(e)}")

            # Run parameter optimization periodically
            if HAS_ADVANCED_MODULES and hasattr(self, 'advanced_trading') and self.advanced_trading is not None:
                try:
                    optimization_results = self.advanced_trading.optimize_parameters(self.download_data)
                    if optimization_results and 'parameters' in optimization_results:
                        params = optimization_results['parameters']

                        # Update trading parameters
                        self.buy_threshold = params.get('buy_threshold', self.buy_threshold)
                        self.position_size_factor = params.get('position_size_factor', self.position_size_factor)
                        self.risk_per_trade = params.get('risk_per_trade', self.risk_per_trade)

                        self.logger.info(f"Updated trading parameters: "
                                       f"buy_threshold={self.buy_threshold:.2f}, "
                                       f"position_size_factor={self.position_size_factor:.2f}, "
                                       f"risk_per_trade={self.risk_per_trade:.2f}")
                except Exception as e:
                    self.logger.error(f"Error optimizing parameters: {str(e)}")

            # Check for arbitrage opportunities
            if HAS_ADVANCED_MODULES and self.arbitrage_detector is not None:
                try:
                    # In a real implementation, this would use real market data from different exchanges
                    # For now, we'll simulate market data for testing
                    self.arbitrage_detector.simulate_market_data(
                        symbols=symbols[:5],  # Use first 5 symbols
                        exchanges=['binance', 'coinbase', 'kraken'],
                        base_prices={symbol: self.last_prices.get(symbol, 100.0) for symbol in symbols[:5]},
                        volatility=0.002
                    )

                    # Detect arbitrage opportunities
                    opportunities = self.arbitrage_detector.detect_arbitrage_opportunities()

                    # Execute best opportunity if available
                    if opportunities:
                        best_opportunity = self.arbitrage_detector.get_best_opportunity()
                        if best_opportunity and best_opportunity['profit_potential'] > 0.005:  # 0.5% profit
                            self.logger.info(f"Executing arbitrage opportunity: {best_opportunity['symbol']} - "
                                            f"Buy at {best_opportunity['buy_price']:.2f} on {best_opportunity['buy_exchange']}, "
                                            f"Sell at {best_opportunity['sell_price']:.2f} on {best_opportunity['sell_exchange']}")

                            # In a real implementation, this would execute the trades
                            # For now, we'll just simulate execution
                            self.arbitrage_detector.execute_arbitrage(best_opportunity, quantity=100)
                except Exception as e:
                    self.logger.error(f"Error checking arbitrage opportunities: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error scanning stocks: {str(e)}")

    def save_state(self):
        """Enhanced state saving with backup"""
        try:
            # Initialize trades_history if it doesn't exist
            if not hasattr(self, 'trades_history'):
                self.trades_history = []

            # Convert positions to JSON-serializable format
            positions_json = {}
            for symbol, position in self.positions.items():
                position_copy = position.copy()
                # Convert datetime objects to strings
                if 'buy_time' in position_copy:
                    position_copy['buy_time'] = position_copy['buy_time'].isoformat()
                if 'buy_date' in position_copy:
                    position_copy['buy_date'] = position_copy['buy_date'].isoformat()
                positions_json[symbol] = position_copy

            # Convert last_prices and highest_prices to JSON-serializable format
            last_prices_json = {}
            for symbol, price in self.last_prices.items():
                # Convert pandas Series to scalar if needed
                if hasattr(price, 'iloc') and len(price) > 0:
                    last_prices_json[symbol] = float(price.iloc[-1])
                else:
                    last_prices_json[symbol] = float(price)

            highest_prices_json = {}
            for symbol, price in self.highest_prices.items():
                # Convert pandas Series to scalar if needed
                if hasattr(price, 'iloc') and len(price) > 0:
                    highest_prices_json[symbol] = float(price.iloc[-1])
                else:
                    highest_prices_json[symbol] = float(price)

            state = {
                'balance': float(self.balance),
                'positions': positions_json,
                'trades': self.trades_history,
                'last_prices': last_prices_json,
                'highest_prices': highest_prices_json,
                'performance_metrics': {
                    'total_trades': int(self.total_trades),
                    'winning_trades': int(self.winning_trades),
                    'total_profit': float(self.total_profit)
                },
                'timestamp': datetime.now().isoformat()
            }

            # Add advanced metrics if available
            if HAS_ADVANCED_MODULES:
                if self.high_frequency_trader is not None:
                    try:
                        hft_metrics = self.high_frequency_trader.get_performance_metrics()
                        state['hft_metrics'] = {
                            'total_executions': hft_metrics['total_executions'],
                            'successful_executions': hft_metrics['successful_executions'],
                            'average_latency_ms': float(hft_metrics['average_latency_ms'])
                        }
                    except Exception as e:
                        self.logger.warning(f"Error saving HFT metrics: {str(e)}")

                if self.arbitrage_detector is not None:
                    try:
                        arb_metrics = self.arbitrage_detector.get_performance_metrics()
                        state['arbitrage_metrics'] = {
                            'total_opportunities': arb_metrics['total_opportunities'],
                            'profitable_opportunities': arb_metrics['profitable_opportunities'],
                            'total_profit': float(arb_metrics['total_profit'])
                        }
                    except Exception as e:
                        self.logger.warning(f"Error saving arbitrage metrics: {str(e)}")

            # Create data directory if it doesn't exist
            if not os.path.exists('data'):
                os.makedirs('data')

            # Save main file
            with open('data/bot_state.json', 'w') as f:
                json.dump(state, f, indent=4)

            # Save backup
            with open(f'data/bot_state_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
                json.dump(state, f, indent=4)

            self.logger.info("State saved successfully")
        except Exception as e:
            self.logger.error(f"Error saving state: {str(e)}")

    def load_state(self):
        """Load bot state from file"""
        try:
            # Create data directory if it doesn't exist
            if not os.path.exists('data'):
                os.makedirs('data')

            if not os.path.exists('data/bot_state.json'):
                self.logger.info("No saved state found")
                # Initialize empty state
                self.positions = {}
                self.last_prices = {}
                self.highest_prices = {}
                return

            with open('data/bot_state.json', 'r') as f:
                state = json.load(f)

            self.balance = state.get('balance', self.initial_balance)
            self.initial_balance = state.get('initial_balance', self.initial_balance)
            self.positions = state.get('positions', {})
            self.last_prices = state.get('last_prices', {})
            self.highest_prices = state.get('highest_prices', {})
            self.total_trades = state.get('total_trades', 0)
            self.winning_trades = state.get('winning_trades', 0)
            self.trades_history = state.get('trades', [])

            # Load performance metrics if available
            if 'performance_metrics' in state:
                metrics = state['performance_metrics']
                self.total_trades = metrics.get('total_trades', self.total_trades)
                self.winning_trades = metrics.get('winning_trades', self.winning_trades)
                self.total_profit = metrics.get('total_profit', self.total_profit)

            # Log loaded state
            self.logger.info(f"Loaded state with balance: ${self.balance:.2f}, positions: {len(self.positions)}")
            self.position_size_factor = state.get('position_size_factor', 0.4)
            self.scan_interval = state.get('scan_interval', 60)

            # Convert string dates back to datetime objects
            for pos in self.positions.values():
                if 'buy_time' in pos:
                    pos['buy_time'] = datetime.fromisoformat(pos['buy_time'])
                if 'buy_date' in pos:
                    pos['buy_date'] = datetime.fromisoformat(pos['buy_date']).date()

            self.logger.info("Bot state loaded successfully")

        except Exception as e:
            self.logger.error(f"Error loading state: {str(e)}")
            # Initialize empty state on error
            self.positions = {}
            self.last_prices = {}
            self.highest_prices = {}

    def run(self, symbols: List[str] = None):
        """Enhanced main trading loop"""
        self.logger.info("Starting trading bot...")

        # Get symbols if not provided
        symbols = symbols or self.get_watchlist()

        # Main trading loop
        try:
            while True:
                try:
                    # Update market conditions
                    if self.microstructure_analyzer is not None:
                        self.microstructure_analyzer.update_market_conditions()

                    # Check existing positions
                    self.check_positions()

                    # Scan for new opportunities
                    self.scan_stocks(symbols)

                    # Update RL model (skip for now as it requires specific parameters)
                    # if self.rl_trader is not None:
                    #     self.rl_trader.update()

                    # Update high-frequency trading metrics
                    if HAS_ADVANCED_MODULES and self.high_frequency_trader is not None:
                        try:
                            metrics = self.high_frequency_trader.get_performance_metrics()
                            self.logger.info(f"HFT metrics: {metrics['total_executions']} executions, "
                                           f"{metrics['success_rate']*100:.1f}% success rate, "
                                           f"{metrics['average_latency_ms']:.2f}ms avg latency")
                        except Exception as e:
                            self.logger.warning(f"Error getting HFT metrics: {str(e)}")

                    # Save state
                    self.save_state()

                    # Log performance
                    self.log_performance()

                    # Generate performance report periodically (every 24 hours)
                    if hasattr(self, 'last_report_time'):
                        time_since_last_report = (datetime.now() - self.last_report_time).total_seconds()
                        if time_since_last_report > 86400:  # 24 hours
                            self.generate_performance_report()
                            self.last_report_time = datetime.now()
                    else:
                        self.last_report_time = datetime.now()

                    # Wait for next scan
                    time.sleep(self.scan_interval)

                except KeyboardInterrupt:
                    self.logger.info("Stopping trading bot...")
                    break
                except Exception as e:
                    self.logger.error(f"Error in main loop: {str(e)}")
                    time.sleep(60)  # Wait a minute before retrying

        except Exception as e:
            self.logger.error(f"Fatal error in trading bot: {str(e)}")

    def generate_performance_report(self, save_path: Optional[str] = None) -> None:
        """Generate a performance report"""
        try:
            if not HAS_ADVANCED_MODULES or not hasattr(self, 'performance_tracker') or self.performance_tracker is None:
                self.logger.warning("Performance tracker not available")
                return

            # Generate HTML report
            html_path = os.path.join(self.data_dir, 'performance_report.html') if save_path is None else save_path
            self.performance_tracker.generate_html_report(html_path)

            # Generate performance plot
            plot_path = os.path.join(self.data_dir, 'performance_plot.png')
            self.performance_tracker.plot_performance(plot_path)

            # Log performance metrics
            report = self.performance_tracker.get_performance_report()
            self.logger.info(f"Performance report generated: {html_path}")
            self.logger.info(f"Total trades: {report['total_trades']}, Win rate: {report['win_rate']:.2%}, "
                           f"Total profit: ${report['total_profit']:.2f}, Sharpe ratio: {report['sharpe_ratio']:.2f}")

            # Return report path
            return html_path
        except Exception as e:
            self.logger.error(f"Error generating performance report: {str(e)}")
            return None

    def log_performance(self):
        """Enhanced performance logging"""
        try:
            # Use performance tracker if available
            if HAS_ADVANCED_MODULES and hasattr(self, 'performance_tracker') and self.performance_tracker is not None:
                report = self.performance_tracker.get_performance_report()

                self.logger.info(f"Performance Update:")
                self.logger.info(f"- Balance: ${self.balance:,.2f}")
                self.logger.info(f"- Total Profit: ${report['total_profit']:,.2f}")
                self.logger.info(f"- Total Trades: {report['total_trades']}")
                self.logger.info(f"- Win Rate: {report['win_rate']:.1%}")
                self.logger.info(f"- Max Drawdown: {report['max_drawdown']:.1%}")
                self.logger.info(f"- Sharpe Ratio: {report['sharpe_ratio']:.2f}")

                # Record portfolio value
                self.performance_tracker.record_portfolio_value(datetime.now(), self.balance + self.get_positions_value())
            else:
                # Fall back to basic logging
                win_rate = self.winning_trades / self.total_trades if self.total_trades > 0 else 0
                profit_pct = (self.balance / self.initial_balance - 1) * 100

                self.logger.info(f"Performance Update:")
                self.logger.info(f"- Balance: ${self.balance:,.2f}")
                self.logger.info(f"- Total Profit: ${self.total_profit:,.2f} ({profit_pct:.2f}%)")
                self.logger.info(f"- Total Trades: {self.total_trades}")
                self.logger.info(f"- Win Rate: {win_rate:.1%}")
                self.logger.info(f"- Max Drawdown: {self.max_drawdown:.1%}")
                self.logger.info(f"- Daily Profit: ${self.daily_profit:,.2f}")

        except Exception as e:
            self.logger.error(f"Error logging performance: {str(e)}")

    def get_watchlist(self):
        """Get watchlist of symbols to trade"""
        # Implementation of get_watchlist method
        # This method should return a list of symbols to be watched
        # For now, we'll use a hardcoded list
        return ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'NVDA', 'AMD', 'TSLA']

    def register_trading_system(self, trading_system):
        """Register an external trading system"""
        if hasattr(trading_system, 'get_positions') and hasattr(trading_system, 'execute_trade'):
            if not hasattr(self, 'trading_systems'):
                self.trading_systems = []
            self.trading_systems.append(trading_system)
            self.logger.info(f"Registered external trading system: {trading_system.__class__.__name__}")
        else:
            self.logger.warning(f"Failed to register trading system: missing required methods")

    def register_quantum_agent(self, quantum_agent):
        """Register a Quantum AI Smart Agent for enhanced decision making

        Args:
            quantum_agent: QuantumAgent instance
        """
        try:
            from quantum_agent import QuantumAgent

            if isinstance(quantum_agent, QuantumAgent):
                self.quantum_agent = quantum_agent
                self.use_quantum_agent = True
                self.logger.info(f"Registered Quantum AI Smart Agent: {quantum_agent.agent_id}")

                # Initialize quantum signals dictionary
                self.quantum_signals = {}

                # Test quantum agent with a sample market data
                try:
                    # Create a small sample data frame for testing
                    import pandas as pd
                    import numpy as np

                    dates = pd.date_range(end=pd.Timestamp.now(), periods=30)
                    sample_data = pd.DataFrame({
                        'Open': np.random.normal(100, 2, 30),
                        'High': np.random.normal(102, 2, 30),
                        'Low': np.random.normal(98, 2, 30),
                        'Close': np.random.normal(101, 2, 30),
                        'Volume': np.random.normal(1000000, 200000, 30)
                    }, index=dates)

                    # Test quantum agent
                    test_symbol = 'TEST'
                    self.quantum_agent.update_beliefs({test_symbol: sample_data})
                    quantum_action = self.quantum_agent.decide_action(test_symbol, sample_data)

                    self.logger.info(f"Quantum AI Smart Agent test successful. Action: {quantum_action['action']}, " +
                                   f"Confidence: {quantum_action['confidence']:.2f}")

                except Exception as e:
                    self.logger.warning(f"Quantum AI Smart Agent test failed: {str(e)}")
            else:
                self.logger.warning(f"Failed to register quantum agent: must be a QuantumAgent instance")
        except Exception as e:
            self.logger.error(f"Failed to register quantum agent: {str(e)}")


    def _get_simulated_order_book(self, symbol: str, data: pd.DataFrame) -> Dict[str, Any]:
        """Generate a simulated order book for testing HFT strategies"""
        try:
            # Get the latest price
            latest_price = data['Close'].iloc[-1]
            if hasattr(latest_price, 'iloc'):
                latest_price = latest_price.iloc[-1]

            # Generate simulated bids and asks
            spread = latest_price * 0.001  # 0.1% spread
            bid_price = latest_price - spread/2
            ask_price = latest_price + spread/2

            # Generate simulated order book
            order_book = {
                'bids': [
                    [bid_price, 100],  # [price, size]
                    [bid_price - 0.01, 200],
                    [bid_price - 0.02, 300],
                    [bid_price - 0.03, 400],
                    [bid_price - 0.04, 500]
                ],
                'asks': [
                    [ask_price, 100],
                    [ask_price + 0.01, 200],
                    [ask_price + 0.02, 300],
                    [ask_price + 0.03, 400],
                    [ask_price + 0.04, 500]
                ]
            }

            return order_book

        except Exception as e:
            self.logger.error(f"Error generating simulated order book for {symbol}: {str(e)}")
            # Return empty order book
            return {'bids': [], 'asks': []}

    def get_data(self, symbol: str, period: str = '6mo') -> pd.DataFrame:
        """Get data for a symbol"""
        try:
            # Check cache first
            if symbol in self.data_cache:
                current_time = datetime.now()
                cache_time = self.last_cache_time.get(symbol)

                # Use cache if it's recent enough
                if cache_time and (current_time - cache_time).total_seconds() < self.cache_timeout:
                    return self.data_cache[symbol]

            # Download fresh data - use 6 months of data to have enough for technical indicators
            data = self.download_data(symbol, period)
            if data is not None and not data.empty:
                # Add technical indicators
                data = self.add_technical_indicators(data)
                return data

            return None

        except Exception as e:
            self.logger.error(f"Error getting data for {symbol}: {str(e)}")

    def reset_simulation_data(self):
        """Reset simulation data to initial values but preserve positions"""
        try:
            # Initialize simulation data if not already done
            if not hasattr(self, 'simulation_positions'):
                self.simulation_positions = {}

            if not hasattr(self, 'live_positions'):
                self.live_positions = {}

            # Store current positions to keep them
            current_positions = self.positions.copy() if hasattr(self, 'positions') else {}

            # Calculate position value
            positions_value = self.get_positions_value()

            # Reset balance to initial value MINUS the positions value
            # This prevents the total portfolio value from changing
            self.balance = self.initial_balance - positions_value

            # Preserve positions if we want to maintain them during simulation
            self.simulation_positions = current_positions

            # Point positions to simulation data
            self.positions = self.simulation_positions

            # Log the reset
            self.logger.info(f"Reset simulation data. Balance: ${self.balance:,.2f}, Positions value: ${positions_value:,.2f}, Total portfolio: ${self.balance + positions_value:,.2f}, Positions kept: {len(self.positions)}")
        except Exception as e:
            self.logger.error(f"Error resetting simulation data: {str(e)}")

    def get_positions_value(self):
        """Calculate the total value of all positions"""
        try:
            total_value = 0.0
            for symbol, quantity in self.positions.items():
                if symbol in self.last_prices:
                    price = self.last_prices[symbol]
                    total_value += price * quantity
            return total_value
        except Exception as e:
            self.logger.error(f"Error calculating positions value: {str(e)}")
            return 0.0

    def switch_to_simulation_mode(self):
        """Switch to simulation mode"""
        try:
            # Initialize simulation positions if not already done
            if not hasattr(self, 'simulation_positions'):
                self.simulation_positions = {}

            # Point positions to simulation data
            self.positions = self.simulation_positions
            self.mode = "simulation"

            self.logger.info(f"Switched to simulation mode. Positions: {len(self.positions)}")
        except Exception as e:
            self.logger.error(f"Error switching to simulation mode: {str(e)}")

    def switch_to_live_mode(self):
        """Switch to live trading mode"""
        try:
            # Initialize live positions if not already done
            if not hasattr(self, 'live_positions'):
                self.live_positions = {}

            # Point positions to live data
            self.positions = self.live_positions
            self.mode = "live"

            self.logger.info(f"Switched to live mode. Positions: {len(self.positions)}")
        except Exception as e:
            self.logger.error(f"Error switching to live mode: {str(e)}")
            return None

    def set_exchange_manager(self, exchange_manager):
        """Set an exchange manager for live trading

        Args:
            exchange_manager: Exchange manager instance for executing trades

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.exchange_manager = exchange_manager

            # Connect execution engine to exchange manager if available
            if hasattr(self, 'execution_engine') and self.execution_engine is not None:
                self.execution_engine.set_exchange_manager(exchange_manager)
                self.logger.info("Connected execution engine to exchange manager")

            # Switch to live mode
            self.switch_to_live_mode()

            # Log available exchanges
            exchanges = exchange_manager.get_available_exchanges()
            self.logger.info(f"Available exchanges: {', '.join(exchanges)}")

            return True
        except Exception as e:
            self.logger.error(f"Error setting exchange manager: {str(e)}")
            return False

    def set_balance(self, balance):
        """Set the initial balance for the trading bot

        Args:
            balance (float): Initial balance amount

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Initialize account if not already done
            if not hasattr(self, 'account'):
                self.account = {
                    'balance': 0,
                    'equity': 0,
                    'positions': {}
                }

            # Set the balance
            self.account['balance'] = float(balance)
            self.account['equity'] = float(balance)

            # Log the balance
            self.logger.info(f"Set initial balance to ${balance:.2f}")

            # Update execution engine if available
            if hasattr(self, 'execution_engine') and self.execution_engine is not None:
                self.execution_engine.set_balance(balance)

            return True
        except Exception as e:
            self.logger.error(f"Error setting balance: {str(e)}")
            return False

    def get_balance(self):
        """Get the current balance

        Returns:
            float: Current balance
        """
        if hasattr(self, 'account'):
            return self.account.get('balance', 0)
        return 0

    def get_equity(self):
        """Get the current equity (balance + unrealized P&L)

        Returns:
            float: Current equity
        """
        if hasattr(self, 'account'):
            return self.account.get('equity', 0)
        return 0

    def get_learning_stats(self):
        """Get statistics about the learning process

        Returns:
            dict: Learning statistics
        """
        stats = {}

        # Get stats from continuous learning engine if available
        if hasattr(self, 'continuous_learning_engine'):
            stats.update(self.continuous_learning_engine.get_learning_stats())

        # Get stats from memory-augmented learning if available
        if hasattr(self, 'memory_augmented_learning'):
            stats.update(self.memory_augmented_learning.get_learning_stats())

        # Get stats from reinforcement learning if available
        if hasattr(self, 'reinforcement_learning'):
            stats.update(self.reinforcement_learning.get_learning_stats())

        return stats

    def get_model_updates(self):
        """Get information about model updates

        Returns:
            dict: Model update information
        """
        updates = {}

        # Get updates from continuous learning engine if available
        if hasattr(self, 'continuous_learning_engine'):
            updates.update(self.continuous_learning_engine.get_model_updates())

        # Get updates from deep learning predictor if available
        if hasattr(self, 'deep_learning_predictor'):
            updates.update(self.deep_learning_predictor.get_model_updates())

        # Get updates from advanced predictor if available
        if hasattr(self, 'advanced_predictor'):
            updates.update(self.advanced_predictor.get_model_updates())

        return updates

    def update_market_data(self, market_data):
        """Update the bot with new market data from simulation

        Args:
            market_data (dict): Market data for various symbols

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Store the market data
            if not hasattr(self, 'current_market_data'):
                self.current_market_data = {}

            # Update with new data
            self.current_market_data.update(market_data)

            # Process the data with various components
            symbols = list(market_data.keys())

            # Update market regime detector if available
            if hasattr(self, 'market_regime_detector') and symbols:
                for symbol in symbols:
                    if symbol in market_data and 'data' in market_data[symbol]:
                        self.market_regime_detector.detect_regime(symbol, market_data[symbol]['data'])

            # Update continuous learning engine if available
            if hasattr(self, 'continuous_learning_engine') and symbols:
                for symbol in symbols:
                    if symbol in market_data and 'data' in market_data[symbol]:
                        self.continuous_learning_engine.process_market_data(symbol, market_data[symbol]['data'])

            # Log the update
            self.logger.debug(f"Updated market data for {len(symbols)} symbols")

            return True
        except Exception as e:
            self.logger.error(f"Error updating market data: {str(e)}")
            return False

    def process_market_data(self):
        """Process the current market data and generate trading signals

        Returns:
            list: List of trading signals generated
        """
        try:
            if not hasattr(self, 'current_market_data') or not self.current_market_data:
                self.logger.warning("No market data available to process")
                return []

            signals = []

            # Process each symbol
            for symbol, data in self.current_market_data.items():
                if 'data' not in data:
                    continue

                # Get the latest data point
                latest_data = data['data'].iloc[-1] if not data['data'].empty else None
                if latest_data is None:
                    continue

                # Generate signals using various strategies
                signal = self._generate_signal_for_symbol(symbol, data['data'])
                if signal:
                    signals.append(signal)

            # Log the signals
            if signals:
                self.logger.info(f"Generated {len(signals)} trading signals")

            return signals
        except Exception as e:
            self.logger.error(f"Error processing market data: {str(e)}")
            return []

    def get_signals(self):
        """Get trading signals from the bot

        Returns:
            list: List of trading signals
        """
        try:
            # Process market data to generate signals
            signals = self.process_market_data()

            # Store signals for later reference
            if not hasattr(self, 'recent_signals'):
                self.recent_signals = []

            # Add new signals to recent signals
            self.recent_signals.extend(signals)

            # Keep only the last 100 signals
            if len(self.recent_signals) > 100:
                self.recent_signals = self.recent_signals[-100:]

            return signals
        except Exception as e:
            self.logger.error(f"Error getting signals: {str(e)}")
            return []

    def get_orders(self):
        """Get orders based on trading signals

        Returns:
            list: List of orders to execute
        """
        try:
            # Get signals
            signals = self.get_signals()

            # Convert signals to orders
            orders = []
            for signal in signals:
                # Skip signals with low confidence
                if signal.get('confidence', 0) < 0.5:
                    continue

                # Create order from signal
                symbol = signal.get('symbol')
                action = signal.get('action')

                if not symbol or not action:
                    continue

                # Determine order size based on position sizing
                order_size = self._calculate_position_size(symbol, action)

                # Create order
                order = {
                    'symbol': symbol,
                    'action': action,
                    'quantity': order_size,
                    'order_type': 'market',  # Default to market order
                    'timestamp': datetime.now(),
                    'signal_source': signal.get('source', 'unknown')
                }

                orders.append(order)

            # Log orders
            if orders:
                self.logger.info(f"Generated {len(orders)} orders from signals")

            return orders
        except Exception as e:
            self.logger.error(f"Error generating orders: {str(e)}")
            return []

    def _calculate_position_size(self, symbol, action):
        """Calculate position size for an order

        Args:
            symbol (str): The symbol to trade
            action (str): The action to take (buy, sell)

        Returns:
            float: Position size
        """
        try:
            # Get account balance
            balance = self.get_balance()

            # Default to using 2% of balance per trade
            risk_per_trade = 0.02

            # Get current price
            current_price = None
            if hasattr(self, 'current_market_data') and symbol in self.current_market_data:
                data = self.current_market_data[symbol].get('data')
                if data is not None and not data.empty:
                    current_price = data['Close'].iloc[-1]

            # If we can't get current price, return 0
            if current_price is None:
                return 0

            # Calculate position size
            position_size = (balance * risk_per_trade) / current_price

            # Round to appropriate precision
            if symbol.startswith('BTC'):
                position_size = round(position_size, 6)  # BTC typically traded to 6 decimal places
            elif symbol.startswith('ETH'):
                position_size = round(position_size, 5)  # ETH typically traded to 5 decimal places
            else:
                position_size = round(position_size, 2)  # Stocks typically traded in whole shares

            return position_size
        except Exception as e:
            self.logger.error(f"Error calculating position size for {symbol}: {str(e)}")
            return 0

    def _generate_signal_for_symbol(self, symbol, data):
        """Generate a trading signal for a specific symbol

        Args:
            symbol (str): The symbol to generate a signal for
            data (DataFrame): Historical data for the symbol

        Returns:
            dict: Trading signal or None if no signal
        """
        try:
            # Use various components to generate signals
            signal = None

            # Use deep learning predictor if available
            if hasattr(self, 'deep_learning_predictor'):
                prediction = self.deep_learning_predictor.predict(symbol, data)
                if prediction and prediction.get('signal'):
                    signal = {
                        'symbol': symbol,
                        'action': prediction['signal'],
                        'confidence': prediction.get('confidence', 0.5),
                        'source': 'deep_learning',
                        'timestamp': datetime.now()
                    }

            # Use advanced predictor if available and no signal yet
            if not signal and hasattr(self, 'advanced_predictor'):
                prediction = self.advanced_predictor.predict(symbol, data)
                if prediction and prediction.get('signal'):
                    signal = {
                        'symbol': symbol,
                        'action': prediction['signal'],
                        'confidence': prediction.get('confidence', 0.5),
                        'source': 'advanced_predictor',
                        'timestamp': datetime.now()
                    }

            # Use traditional analysis as fallback
            if not signal:
                # Simple moving average crossover strategy
                if len(data) >= 50:
                    sma20 = data['Close'].rolling(window=20).mean()
                    sma50 = data['Close'].rolling(window=50).mean()

                    # Check for crossover
                    if sma20.iloc[-2] < sma50.iloc[-2] and sma20.iloc[-1] > sma50.iloc[-1]:
                        signal = {
                            'symbol': symbol,
                            'action': 'buy',
                            'confidence': 0.6,
                            'source': 'moving_average_crossover',
                            'timestamp': datetime.now()
                        }
                    elif sma20.iloc[-2] > sma50.iloc[-2] and sma20.iloc[-1] < sma50.iloc[-1]:
                        signal = {
                            'symbol': symbol,
                            'action': 'sell',
                            'confidence': 0.6,
                            'source': 'moving_average_crossover',
                            'timestamp': datetime.now()
                        }

            return signal
        except Exception as e:
            self.logger.error(f"Error generating signal for {symbol}: {str(e)}")
            return None

    def register_quantum_agent(self, quantum_agent):
        """Register a Quantum AI Smart Agent for enhanced decision making

        Args:
            quantum_agent: QuantumAgent instance
        """
        try:
            from quantum_agent import QuantumAgent

            if isinstance(quantum_agent, QuantumAgent):
                self.quantum_agent = quantum_agent
                self.use_quantum_agent = True
                self.logger.info(f"Registered Quantum AI Smart Agent: {quantum_agent.agent_id}")
                return True
            else:
                self.logger.warning(f"Failed to register quantum agent: not a QuantumAgent instance")
                return False
        except Exception as e:
            self.logger.error(f"Error registering quantum agent: {str(e)}")
            return False

    def set_data_provider(self, data_provider):
        """Set an external data provider for the trading bot

        Args:
            data_provider: Data provider instance with get_data and get_realtime_data methods
        """
        try:
            # Store the original data provider methods for fallback
            if not hasattr(self, 'original_get_data'):
                self.original_get_data = self.get_data

            if not hasattr(self, 'original_download_data'):
                self.original_download_data = self.download_data

            # Store the data provider
            self.external_data_provider = data_provider

            # Override the get_data method to use the external provider
            def enhanced_get_data(symbol, period='6mo', interval='1d'):
                try:
                    # Try to get data from external provider
                    data = self.external_data_provider.get_data(symbol, period=period, interval=interval)
                    if data is not None and not data.empty:
                        return data

                    # Fall back to original method if external provider fails
                    self.logger.warning(f"External data provider failed for {symbol}, falling back to original method")
                    return self.original_get_data(symbol, period)
                except Exception as e:
                    self.logger.error(f"Error in enhanced get_data for {symbol}: {str(e)}")
                    return self.original_get_data(symbol, period)

            # Replace the get_data method
            self.get_data = enhanced_get_data.__get__(self, self.__class__)

            # Override the download_data method to use the external provider
            def enhanced_download_data(symbol, period='6mo', interval='1d'):
                try:
                    # Try to get data from external provider
                    data = self.external_data_provider.get_data(symbol, period=period, interval=interval)
                    if data is not None and not data.empty:
                        return data

                    # Fall back to original method if external provider fails
                    self.logger.warning(f"External data provider failed for {symbol}, falling back to original method")
                    return self.original_download_data(symbol, period, interval)
                except Exception as e:
                    self.logger.error(f"Error in enhanced download_data for {symbol}: {str(e)}")
                    return self.original_download_data(symbol, period, interval)

            # Replace the download_data method
            self.download_data = enhanced_download_data.__get__(self, self.__class__)

            self.logger.info(f"Set external data provider: {data_provider.__class__.__name__}")
            return True
        except Exception as e:
            self.logger.error(f"Error setting data provider: {str(e)}")
            return False

    def get_status(self):
        '''Get the current status of the trading bot.'''
        status = {
            'balance': self.balance,
            'positions': self.positions,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'total_profit': self.total_profit,
            'daily_profit': self.daily_profit,
            'max_drawdown': self.max_drawdown,
            'watched_symbols': self.watched_symbols,
            'last_prices': self.last_prices
        }

        return status

    def get_portfolio_value(self):
        '''Get the current portfolio value.'''
        try:
            # Calculate portfolio value based on positions and current prices
            portfolio_value = self.balance

            # Add value of all positions
            for symbol, position in self.positions.items():
                if symbol in self.last_prices:
                    current_price = self.last_prices[symbol]
                    position_value = position['quantity'] * current_price
                    portfolio_value += position_value

            return portfolio_value
        except Exception as e:
            self.logger.error(f"Error calculating portfolio value: {str(e)}")
            return self.balance  # Return just the balance if there's an error

    def get_positions(self):
        '''Get the current positions.'''
        try:
            # Return a copy of the positions dictionary to avoid external modification
            return self.positions.copy()
        except Exception as e:
            self.logger.error(f"Error getting positions: {str(e)}")
            return {}

    def get_watchlist(self):
        '''Get the current watchlist.'''
        try:
            # Return a copy of the watched symbols list
            return self.watched_symbols.copy()
        except Exception as e:
            self.logger.error(f"Error getting watchlist: {str(e)}")
            return ["BTC/USD", "ETH/USD", "AAPL", "MSFT", "GOOGL"]  # Return default symbols if there's an error

    def get_watchlist_data(self):
        """Get detailed watchlist data"""
        try:
            watchlist = []
            symbols = self.get_watchlist()

            for symbol in symbols:
                try:
                    # Get data for the symbol
                    data = self.get_data(symbol)

                    if data is not None and not data.empty:
                        # Get latest price
                        price = data['Close'].iloc[-1]

                        # Get previous price for calculating change
                        prev_price = data['Close'].iloc[-2] if len(data) > 1 else price

                        # Calculate change percentage
                        change = (price / prev_price - 1) * 100

                        # Get volume
                        volume = data['Volume'].iloc[-1] if 'Volume' in data.columns else 0

                        # Get signal (if available)
                        signal = "Neutral"  # Default signal
                        if hasattr(self, 'get_signal'):
                            try:
                                signal = self.get_signal(symbol)
                            except Exception:
                                pass

                        # Add to watchlist
                        watchlist.append({
                            'symbol': symbol,
                            'price': price,
                            'change': change,
                            'volume': volume,
                            'signal': signal
                        })
                except Exception as e:
                    self.logger.error(f"Error processing {symbol} for watchlist: {str(e)}")
                    # Add with default values
                    watchlist.append({
                        'symbol': symbol,
                        'price': 0.0,
                        'change': 0.0,
                        'volume': 0,
                        'signal': "Error"
                    })

            return watchlist
        except Exception as e:
            self.logger.error(f"Error getting watchlist data: {str(e)}")
            return []

    def get_data(self, symbol, timeframe='1d', limit=100, period=None):
        """Get historical data for a symbol

        Args:
            symbol: Trading symbol
            timeframe: Timeframe for data (e.g., '1m', '1h', '1d')
            limit: Number of data points to retrieve
            period: Optional period string (e.g., '1y', '6mo', '1mo')

        Returns:
            DataFrame with historical data
        """
        try:
            # Check if we have a data provider
            if not hasattr(self, 'data_provider') or self.data_provider is None:
                self.logger.error("No data provider available")
                return pd.DataFrame()

            # Convert period to limit if provided
            if period is not None:
                # Convert period string to approximate number of candles
                if period.endswith('y'):  # years
                    years = int(period[:-1])
                    limit = years * 365  # Approximate days in a year
                elif period.endswith('mo'):  # months
                    months = int(period[:-2])
                    limit = months * 30  # Approximate days in a month
                elif period.endswith('w'):  # weeks
                    weeks = int(period[:-1])
                    limit = weeks * 7  # Days in a week
                elif period.endswith('d'):  # days
                    limit = int(period[:-1])

            # Get data from provider
            data = self.data_provider.get_historical_data(symbol, timeframe, limit)

            # Add technical indicators if data is not empty
            if data is not None and not data.empty:
                # Add indicators
                if hasattr(self, 'add_indicators'):
                    data = self.add_indicators(data)

                # Add safe indicators if available
                if 'HAS_SAFE_INDICATORS' in globals() and HAS_SAFE_INDICATORS:
                    data = add_safe_indicators(data)

            return data
        except Exception as e:
            self.logger.error(f"Error getting data for {symbol}: {str(e)}")
            return pd.DataFrame()

    def load_state(self, filepath):
        """Load bot state from file

        Args:
            filepath: Path to state file

        Returns:
            True if successful, False otherwise
        """
        try:
            if not os.path.exists(filepath):
                self.logger.warning(f"State file not found: {filepath}")
                return False

            with open(filepath, 'r') as f:
                state = json.load(f)

            # Load basic state
            if 'balance' in state:
                self.balance = state['balance']

            if 'initial_balance' in state:
                self.initial_balance = state['initial_balance']

            if 'positions' in state:
                self.positions = state['positions']

            if 'watched_symbols' in state:
                self.watched_symbols = state['watched_symbols']

            if 'trade_history' in state:
                self.trade_history = state['trade_history']

            # Load agent states if available
            if 'agent_states' in state and hasattr(self, 'agents'):
                for agent_name, agent_state in state['agent_states'].items():
                    for agent in self.agents:
                        if hasattr(agent, 'name') and agent.name == agent_name:
                            if hasattr(agent, 'load_state'):
                                agent.load_state(agent_state)

            self.logger.info(f"Loaded state from {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Error loading state: {str(e)}")
            return False

    def get_return(self):
        '''Get the current return on investment.'''
        try:
            # Calculate return based on initial balance and current portfolio value
            initial_value = self.initial_balance
            current_value = self.get_portfolio_value()

            if initial_value > 0:
                return_value = (current_value - initial_value) / initial_value * 100
                return return_value
            else:
                return 0.0
        except Exception as e:
            self.logger.error(f"Error calculating return: {str(e)}")
            return 0.0


# Create alias for backward compatibility
TradingBot = HybridTradingBot
