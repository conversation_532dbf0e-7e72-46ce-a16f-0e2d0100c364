#!/usr/bin/env python3
"""
Test script to check if all required imports are working
"""

import sys
print(f"Python version: {sys.version}")

try:
    import pandas as pd
    print("✓ pandas imported successfully")
except ImportError as e:
    print(f"✗ pandas import failed: {e}")

try:
    import numpy as np
    print("✓ numpy imported successfully")
except ImportError as e:
    print(f"✗ numpy import failed: {e}")

try:
    import yfinance as yf
    print("✓ yfinance imported successfully")
except ImportError as e:
    print(f"✗ yfinance import failed: {e}")

try:
    from datetime import datetime, timedelta
    print("✓ datetime imported successfully")
except ImportError as e:
    print(f"✗ datetime import failed: {e}")

try:
    import json
    print("✓ json imported successfully")
except ImportError as e:
    print(f"✗ json import failed: {e}")

try:
    import os
    print("✓ os imported successfully")
except ImportError as e:
    print(f"✗ os import failed: {e}")

try:
    import logging
    print("✓ logging imported successfully")
except ImportError as e:
    print(f"✗ logging import failed: {e}")

try:
    from typing import List, Optional, Dict, Any, Tuple, Union
    print("✓ typing imported successfully")
except ImportError as e:
    print(f"✗ typing import failed: {e}")

try:
    import ta
    print("✓ ta imported successfully")
except ImportError as e:
    print(f"✗ ta import failed: {e}")

try:
    import time
    print("✓ time imported successfully")
except ImportError as e:
    print(f"✗ time import failed: {e}")

try:
    import requests
    print("✓ requests imported successfully")
except ImportError as e:
    print(f"✗ requests import failed: {e}")

try:
    import threading
    print("✓ threading imported successfully")
except ImportError as e:
    print(f"✗ threading import failed: {e}")

try:
    import multiprocessing
    print("✓ multiprocessing imported successfully")
except ImportError as e:
    print(f"✗ multiprocessing import failed: {e}")

try:
    from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
    print("✓ concurrent.futures imported successfully")
except ImportError as e:
    print(f"✗ concurrent.futures import failed: {e}")

try:
    from dotenv import load_dotenv
    print("✓ dotenv imported successfully")
except ImportError as e:
    print(f"✗ dotenv import failed: {e}")

# Test basic functionality
try:
    # Test numpy array creation
    arr = np.array([1, 2, 3], dtype=np.float32)
    print(f"✓ numpy array creation successful: {arr}")
except Exception as e:
    print(f"✗ numpy array creation failed: {e}")

try:
    # Test pandas DataFrame creation
    df = pd.DataFrame({'A': [1, 2, 3], 'B': [4, 5, 6]})
    print(f"✓ pandas DataFrame creation successful: shape {df.shape}")
except Exception as e:
    print(f"✗ pandas DataFrame creation failed: {e}")

print("\nAll basic imports tested!")
