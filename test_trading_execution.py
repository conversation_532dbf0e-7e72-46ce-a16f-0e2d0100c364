#!/usr/bin/env python3
"""
Test trading execution to verify the system is actually trading
"""

import sys
import time
from datetime import datetime

def test_trading_execution():
    """Test if the trading system is actually executing trades"""
    print("🔥 TESTING TRADING EXECUTION")
    print("=" * 60)
    
    try:
        from trading_bot_new import TradingBot
        
        # Create bot instance
        bot = TradingBot()
        
        # Check initial state
        print(f"✅ Initial balance: ${bot.balance:.2f}")
        print(f"✅ Initial positions: {len(bot.positions)}")
        
        # Switch to simulation mode
        bot.switch_to_simulation_mode()
        print(f"✅ Switched to simulation mode")
        
        # Enable fast trading
        bot.fast_trading_mode = True
        bot.scalping_mode = True
        print(f"✅ Fast trading enabled")
        
        # Test a single trading cycle
        print("\n🚀 TESTING SINGLE TRADING CYCLE")
        print("-" * 40)
        
        # Get some test data
        test_symbol = "AAPL"
        data = bot.get_data(test_symbol)
        
        if data is not None and len(data) > 0:
            current_price = data['Close'].iloc[-1]
            print(f"✅ Got data for {test_symbol}: ${current_price:.2f}")
            
            # Test buy decision
            if bot.balance >= current_price:
                # Try to buy
                success = bot.buy_stock(test_symbol, 1, current_price)
                if success:
                    print(f"🎯 BUY EXECUTED: {test_symbol} @ ${current_price:.2f}")
                    print(f"   New balance: ${bot.balance:.2f}")
                    print(f"   Positions: {len(bot.positions)}")
                    
                    # Wait a moment then try to sell
                    time.sleep(1)
                    
                    # Test sell decision
                    if test_symbol in bot.positions:
                        sell_success = bot.sell_stock(test_symbol, current_price * 1.001, "Test profit")
                        if sell_success:
                            print(f"💰 SELL EXECUTED: {test_symbol} @ ${current_price * 1.001:.2f}")
                            print(f"   Final balance: ${bot.balance:.2f}")
                            print(f"   Positions: {len(bot.positions)}")
                        else:
                            print("❌ Sell failed")
                    else:
                        print("❌ No position to sell")
                else:
                    print("❌ Buy failed")
            else:
                print(f"❌ Insufficient balance: ${bot.balance:.2f} < ${current_price:.2f}")
        else:
            print(f"❌ No data for {test_symbol}")
        
        # Test the fast trading cycle method
        print("\n🚀 TESTING FAST TRADING CYCLE")
        print("-" * 40)
        
        try:
            bot._execute_fast_trading_cycle()
            print("✅ Fast trading cycle executed")
        except Exception as e:
            print(f"❌ Fast trading cycle failed: {e}")
        
        # Test run_simulation method
        print("\n🚀 TESTING RUN_SIMULATION METHOD")
        print("-" * 40)
        
        try:
            # Run a very short simulation
            original_running = bot.running
            bot.running = True
            
            # Override max trades for quick test
            result = bot.run_simulation()
            
            bot.running = original_running
            
            if result:
                print("✅ run_simulation completed successfully")
            else:
                print("❌ run_simulation failed")
                
        except Exception as e:
            print(f"❌ run_simulation error: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("🎯 TRADING EXECUTION TEST RESULTS:")
        print(f"   Final balance: ${bot.balance:.2f}")
        print(f"   Final positions: {len(bot.positions)}")
        print(f"   Fast trading mode: {getattr(bot, 'fast_trading_mode', False)}")
        print(f"   Scalping mode: {getattr(bot, 'scalping_mode', False)}")
        
        # Check if we have the required methods
        required_methods = ['run_simulation', '_execute_fast_trading_cycle', 'check_positions']
        missing_methods = []
        
        for method in required_methods:
            if not hasattr(bot, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ Missing methods: {missing_methods}")
            return False
        else:
            print("✅ All required methods present")
            return True
        
    except Exception as e:
        print(f"❌ Error in trading execution test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run trading execution test"""
    print("🔥 TRADING EXECUTION VERIFICATION")
    print("=" * 60)
    
    success = test_trading_execution()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 TRADING EXECUTION TEST PASSED!")
        print("💰 The system should now be able to:")
        print("   • Execute buy orders")
        print("   • Execute sell orders") 
        print("   • Run fast trading cycles")
        print("   • Complete simulation runs")
    else:
        print("⚠️  TRADING EXECUTION TEST FAILED!")
        print("🔧 The system needs fixes before trading")
    
    print("=" * 60)
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
