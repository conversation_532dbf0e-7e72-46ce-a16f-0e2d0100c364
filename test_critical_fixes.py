#!/usr/bin/env python3
"""
Test script to verify critical fixes in the trading system
"""

import sys
import traceback
import pandas as pd
import numpy as np

def test_basic_imports():
    """Test basic imports"""
    print("Testing basic imports...")
    try:
        import pandas as pd
        print("✓ pandas imported")
        
        import numpy as np
        print("✓ numpy imported")
        
        import yfinance as yf
        print("✓ yfinance imported")
        
        return True
    except Exception as e:
        print(f"✗ Basic import failed: {e}")
        return False

def test_trading_bot_import():
    """Test trading bot import"""
    print("\nTesting trading bot import...")
    try:
        from trading_bot_new import TradingBot
        print("✓ TradingBot imported successfully")
        return True
    except Exception as e:
        print(f"✗ TradingBot import failed: {e}")
        traceback.print_exc()
        return False

def test_trading_bot_instantiation():
    """Test trading bot instantiation"""
    print("\nTesting trading bot instantiation...")
    try:
        from trading_bot_new import TradingBot
        bot = TradingBot()
        print("✓ TradingBot instantiated successfully")
        return True
    except Exception as e:
        print(f"✗ TradingBot instantiation failed: {e}")
        traceback.print_exc()
        return False

def test_rl_state_method():
    """Test the _get_state_for_rl method"""
    print("\nTesting RL state method...")
    try:
        from trading_bot_new import TradingBot
        bot = TradingBot()
        
        # Create sample data
        data = pd.DataFrame({
            'Close': [100, 101, 102, 103, 104],
            'Volume': [1000, 1100, 1200, 1300, 1400],
            'RSI': [45, 50, 55, 60, 65],
            'MACD': [-0.5, 0, 0.5, 1.0, 1.5]
        })
        
        state = bot._get_state_for_rl(data)
        print(f"✓ RL state created successfully: shape {state.shape}")
        print(f"  State values: {state}")
        return True
    except Exception as e:
        print(f"✗ RL state method failed: {e}")
        traceback.print_exc()
        return False

def test_analyze_stock_method():
    """Test the analyze_stock method with fixed sentiment_score"""
    print("\nTesting analyze_stock method...")
    try:
        from trading_bot_new import TradingBot
        bot = TradingBot()
        
        # Create sample data
        data = pd.DataFrame({
            'Close': [100, 101, 102, 103, 104],
            'Volume': [1000, 1100, 1200, 1300, 1400],
            'High': [101, 102, 103, 104, 105],
            'Low': [99, 100, 101, 102, 103],
            'Open': [100, 101, 102, 103, 104]
        })
        
        score = bot.analyze_stock("AAPL", data)
        print(f"✓ analyze_stock completed successfully: score = {score}")
        return True
    except Exception as e:
        print(f"✗ analyze_stock method failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("CRITICAL FIXES VERIFICATION TEST")
    print("=" * 60)
    
    tests = [
        test_basic_imports,
        test_trading_bot_import,
        test_trading_bot_instantiation,
        test_rl_state_method,
        test_analyze_stock_method
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL CRITICAL FIXES VERIFIED!")
        print("The trading system should now work without the previous errors.")
    else:
        print("⚠️  Some issues remain. Check the error messages above.")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
