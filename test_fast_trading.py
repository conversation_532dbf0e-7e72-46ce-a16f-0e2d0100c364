#!/usr/bin/env python3
"""
Quick test to verify fast trading logic is working
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_fast_trading_parameters():
    """Test the fast trading parameters"""
    print("🚀 TESTING FAST TRADING PARAMETERS")
    print("=" * 50)
    
    try:
        from trading_bot_new import TradingBot
        
        # Create bot instance
        bot = TradingBot()
        
        # Check fast trading parameters
        print(f"✅ Fast trading mode: {getattr(bot, 'fast_trading_mode', 'NOT SET')}")
        print(f"✅ Min profit threshold: {getattr(bot, 'min_profit_threshold', 'NOT SET')}")
        print(f"✅ Max hold time (minutes): {getattr(bot, 'max_hold_time_minutes', 'NOT SET')}")
        print(f"✅ Scalping mode: {getattr(bot, 'scalping_mode', 'NOT SET')}")
        print(f"✅ Stop loss: {getattr(bot, 'stop_loss', 'NOT SET')}")
        print(f"✅ Take profit: {getattr(bot, 'take_profit', 'NOT SET')}")
        
        # Test LLM sell decision method
        print("\n🧠 TESTING LLM SELL DECISION METHOD")
        print("=" * 50)
        
        # Create sample data
        sample_data = pd.DataFrame({
            'Close': [100, 101, 102, 103, 104],
            'Volume': [1000, 1100, 1200, 1300, 1400],
            'RSI': [45, 50, 55, 60, 65],
            'MACD': [-0.5, 0, 0.5, 1.0, 1.5]
        })
        
        # Create sample position
        sample_position = {
            'entry_price': 100,
            'entry_time': datetime.now() - timedelta(minutes=20),
            'shares': 10
        }
        
        # Test LLM decision with profit
        profit_pct = 0.04  # 4% profit
        time_held = 20  # 20 minutes
        
        decision = bot._get_llm_sell_decision("AAPL", sample_data, sample_position, profit_pct, time_held)
        
        print(f"✅ LLM Decision: {decision}")
        print(f"   Should sell: {decision['should_sell']}")
        print(f"   Reason: {decision['reason']}")
        print(f"   Confidence: {decision['confidence']:.1%}")
        
        # Test with different scenarios
        print("\n📊 TESTING DIFFERENT SCENARIOS")
        print("=" * 50)
        
        scenarios = [
            {"profit": 0.005, "time": 10, "desc": "Small profit, short time"},
            {"profit": 0.01, "time": 30, "desc": "Good profit, long time"},
            {"profit": -0.005, "time": 5, "desc": "Small loss, short time"},
            {"profit": 0.002, "time": 16, "desc": "Tiny profit, medium time"}
        ]
        
        for scenario in scenarios:
            decision = bot._get_llm_sell_decision("TEST", sample_data, sample_position, 
                                                scenario["profit"], scenario["time"])
            print(f"📈 {scenario['desc']}: {'SELL' if decision['should_sell'] else 'HOLD'} "
                  f"({decision['confidence']:.1%} confidence)")
        
        print("\n🎯 FAST TRADING LOGIC VERIFICATION")
        print("=" * 50)
        
        # Verify aggressive parameters
        if hasattr(bot, 'min_profit_threshold') and bot.min_profit_threshold <= 0.005:
            print("✅ Aggressive profit threshold: GOOD")
        else:
            print("❌ Profit threshold too conservative")
            
        if hasattr(bot, 'max_hold_time_minutes') and bot.max_hold_time_minutes <= 30:
            print("✅ Fast hold time limit: GOOD")
        else:
            print("❌ Hold time too long")
            
        if hasattr(bot, 'stop_loss') and bot.stop_loss <= 0.01:
            print("✅ Tight stop loss: GOOD")
        else:
            print("❌ Stop loss too loose")
            
        if hasattr(bot, 'take_profit') and bot.take_profit <= 0.02:
            print("✅ Quick take profit: GOOD")
        else:
            print("❌ Take profit too high")
            
        print("\n🚀 FAST TRADING SYSTEM STATUS: READY FOR AGGRESSIVE PROFIT GENERATION!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing fast trading: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run fast trading test"""
    print("🔥 FAST TRADING VERIFICATION TEST")
    print("=" * 60)
    
    success = test_fast_trading_parameters()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 FAST TRADING SYSTEM IS READY!")
        print("💰 The system should now:")
        print("   • Sell positions quickly for small profits")
        print("   • Use LLM decisions for intelligent exits")
        print("   • Hold positions for maximum 30 minutes")
        print("   • Cut losses fast at 0.5%")
        print("   • Take profits at 1%")
        print("   • Use scalping for tiny profits")
    else:
        print("⚠️  Fast trading system needs fixes")
    
    print("=" * 60)
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
